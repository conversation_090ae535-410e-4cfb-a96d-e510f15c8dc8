{{ define "main" }}
    <!-- override Prism's default copy messages to 'empty'. Required by CSS styling.
         see: https://prismjs.com/plugins/copy-to-clipboard/#styling -->
    <div data-prismjs-copy="" data-prismjs-copy-success="" data-prismjs-copy-error="">
        {{ .Content }}
    </div>

    <!-- change -->
    {{ if .Site.Params.docs.repoURL -}}
        {{ partial (printf "%s/%s" ($.Scratch.Get "pathName") "gitinfo") . }}
    {{ end -}}

    {{ if .Page.Store.Get "hasMermaid" }}
    {{ $mermaid := resources.Get (printf "%s/%s" ($.Scratch.Get "pathName") "js/mermaid.min.js") }}
    {{ if hugo.IsProduction }}
        {{ $mermaid = $mermaid | fingerprint "sha384" }}
    {{ end }}
    <script src="{{ $mermaid.RelPermalink }}" {{ if hugo.IsProduction }}integrity="{{ $mermaid.Data.Integrity }}"{{ end }}></script>
    <script>
        const config = {
            startOnLoad:true,
            logLevel: "error",
            align: "center",
            theme:"null"
        };
        mermaid.initialize(config);
    </script>
    {{ end }}

    {{ if .Site.Params.feedback.enabled | default false -}}
        {{ if or (.Site.Params.plausible.dataDomain) (.Site.GoogleAnalytics) }}
            {{- partial (printf "%s/%s" ($.Scratch.Get "pathName") "footer/feedback.html") . -}}
        {{ else }}
            {{ errorf "Either Google Analytics or Plausible Analytics must be configured before enabling the Feedback Widget." }}
        {{ end }}
    {{ end -}}

{{ end }}