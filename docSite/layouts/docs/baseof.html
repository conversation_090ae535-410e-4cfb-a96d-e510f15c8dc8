<!DOCTYPE html>
{{ $.Scratch.Delete "social_list" }}
{{ $.Scratch.Set "pathName" (printf "%s" (.Site.Params.docs.pathName | default "docs")) }}
<!-- social_list -->
<!-- change -->
{{ $social_params := slice "github" "twitter" "instagram" "rss" "wechat" }}
{{ range $social_params }}
    {{ if isset site.Params.social . }}
        {{ $.Scratch.Add "social_list" (slice .) }}
    {{ end }}
{{ end }}
<html lang="{{ site.LanguageCode }}">
    {{- partial (printf "%s/%s" ($.Scratch.Get "pathName") "head.html") . -}}
<body>
    <div class="content">
        <div class="page-wrapper toggled">
            {{- partial (printf "%s/%s" ($.Scratch.Get "pathName") "sidebar.html") . -}}
                <!-- Start Page Content -->
                <main class="page-content bg-transparent">
                    {{- partialCached (printf "%s/%s" ($.Scratch.Get "pathName") "top-header.html") . -}}
                        <div class="container-fluid">
                            <div class="layout-spacing">
                                {{ $currentPage := . -}}
                                {{ if site.Params.docs.breadcrumbs | default true }}
                                    <div class="d-md-flex justify-content-between align-items-center">
                                        {{- partial (printf "%s/%s" ($.Scratch.Get "pathName") "breadcrumbs.html") . -}}
                                    </div>
                                {{ end }}
                                <div class="row flex-xl-nowrap">
                                    {{ if site.Params.docs.toc | default true }}
                                    <div class="docs-toc col-xl-3 {{ if .IsNode }}visually-hidden{{ else }}{{end}} {{ if and (ne .Params.toc false) (ne .TableOfContents "<nav id=\"TableOfContents\"></nav>") }}{{ else }}visually-hidden{{ end }} {{ if site.Params.docs.toc | default true }}{{ else }}visually-hidden{{ end }} d-xl-block">
                                        {{- partial (printf "%s/%s" ($.Scratch.Get "pathName") "toc.html") . -}}
                                    </div>
                                    {{ end }}
                                    {{ if site.Params.docs.tocMobile | default true }}
                                    <div class="docs-toc-mobile {{ if .IsNode }}visually-hidden{{ else }}{{end}} {{ if and (ne .Params.toc false) (ne .TableOfContents "<nav id=\"TableOfContents\"></nav>") }}{{ else }}visually-hidden{{ end }} {{ if site.Params.docs.tocMobile | default true }}{{ else }}visually-hidden{{ end }} d-print-none d-xl-none">
                                        <button id="toc-dropdown-btn" class="btn-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown" data-bs-offset="0,0" aria-expanded="false">
                                            Table of Contents
                                        </button>
                                        {{- partial (printf "%s/%s" ($.Scratch.Get "pathName") "toc-mobile.html") . -}}
                                    </div>
                                    {{ end -}}
                                    <!-- change -->
                                    <div class="docs-content col-12 {{ if .IsNode }}{{ else }}{{ if site.Params.docs.toc | default true }}{{ if and (ne .Params.toc false) }}col-xl-9{{else}}{{end}}{{ else }}{{ end }}{{ end }} mt-0">
                                        <div class="mb-0 d-flex">
                                            {{ if site.Params.docs.titleIcon | default false }}
                                            <i class="material-icons title-icon me-2">{{- .Params.icon | default "article" }}</i>
                                            {{ end }}
                                            <h1 class="content-title mb-0">
                                                {{ $currentPage.Title }}
                                                {{ if .Draft }}
                                                    <span class="badge bg-default fs-6 mb-1 align-middle">DRAFT</span>
                                                {{ end }}
                                            </h1>
                                        </div>
                                        {{ if site.Params.docs.descriptions | default false }}
                                            <p class="lead mb-3">{{ $currentPage.Description | markdownify }}</p>
                                        {{ end }}
                                        <div id="content" class="main-content" {{ if eq .Site.Params.docs.toc true -}}data-bs-spy="scroll" data-bs-root-margin="0px 0px -65%" data-bs-target="#toc-mobile"{{ end }}>
                                            {{ block "main" . }}{{ end }}
                                        </div>
                                        <div>
                                            {{- partial (printf "%s/%s" ($.Scratch.Get "pathName") "doc-nav.html") . -}}
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        {{- partialCached (printf "%s/%s" ($.Scratch.Get "pathName") "footer.html") . -}}

                </main>
        </div>
    </div>

    {{ if site.Params.docs.backToTop | default true }}
    <!-- Back to top -->
    <button onclick="topFunction()" id="back-to-top" aria-label="Back to Top Button" class="back-to-top fs-5"><svg width="24" height="24"><path d="M12,10.224l-6.3,6.3L4.32,15.152,12,7.472l7.68,7.68L18.3,16.528Z" style="fill:#fff"/></svg></button>
    <!-- Back to top -->
    {{ end }}

    <!-- Dark Mode Switch JS -->
    {{ if eq .Site.Params.docs.darkMode true -}}
        {{ $darkModeSwitch := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "js/darkmode-switch.js") | js.Build | minify }}
        <script>{{ $darkModeSwitch.Content | safeJS }}</script>
    {{ end -}}

    {{- partialCached (printf "%s/%s" ($.Scratch.Get "pathName") "footer/footer-scripts.html") . -}}

    <!-- DocSearch Config -->
    {{ if and (.Site.Params.docsearch.appID) (.Site.Params.docsearch.apiKey) -}}
        {{- partialCached (printf "%s/%s" ($.Scratch.Get "pathName") "footer/docsearch.html") . -}}
    {{ end }}

    <!-- FlexSearch Config -->
    {{ if or (not (isset .Site.Params.flexsearch "enabled")) (eq .Site.Params.flexsearch.enabled true) -}}
        {{ if and (.Site.Params.docsearch.appID) (.Site.Params.docsearch.apiKey) -}}
        {{ else }}
            {{- partialCached (printf "%s/%s" ($.Scratch.Get "pathName") "footer/flexsearch.html") . -}}
        {{ end }}
    {{ end }}
</body>
{{- partial (printf "%s/%s" ($.Scratch.Get "pathName") "modals.html") . -}}
</html>