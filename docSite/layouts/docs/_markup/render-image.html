{{ $dest := .Destination }}
{{ $text := .PlainText }}
{{ $url := urls.Parse $dest }}

{{ if .Title }}
  <figure>
    {{ if eq $url.Scheme "" }}
      {{ with $image := resources.Get $dest }}
        {{ if eq $image.MediaType.SubType "svg" }}
          {{ $image.Content | safeHTML }}
        {{ else }}
          <img src="{{ $image.RelPermalink | safeURL }}" alt="{{ $text }}" width="{{ $image.Width }}" height="{{ $image.Height }}" loading="lazy" class="medium-zoom-image">
        {{ end }}
      {{ end }}
    {{ else }}
      <img src="{{ $dest | safeURL }}" alt="{{ $text }}" loading="lazy" class="medium-zoom-image">
    {{ end }}
    <figcaption>{{ .Title | markdownify }}</figcaption>
  </figure>
{{ else }}
  {{ if eq $url.Scheme "" }}
    {{ with $image := resources.Get $dest }}
      {{ if eq $image.MediaType.SubType "svg" }}
        {{ $image.Content | safeHTML }}
      {{ else }}
        <img src="{{ $image.RelPermalink | safeURL }}" alt="{{ $text }}" width="{{ $image.Width }}" height="{{ $image.Height }}" loading="lazy" class="medium-zoom-image">
      {{ end }}
    {{ end }}
  {{ else }}
    <img src="{{ $dest | safeURL }}" alt="{{ $text }}" loading="lazy" class="medium-zoom-image">
  {{ end }}
{{ end }}