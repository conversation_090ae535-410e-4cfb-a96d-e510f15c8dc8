{{ if .Get "default" }}
  {{ template "_internal/shortcodes/figure.html" . }}
{{ else }}
  {{ $url := urls.Parse (.Get "src") }}
  {{ $altText := .Get "alt" }}
  {{ $caption := .Get "caption" }}
  {{ $href := .Get "href" }}
  {{ $class := .Get "class" }}
  <figure{{ with $class }} class="{{ . }}"{{ end }}>
    {{ with $href }}<a href="{{ . }}">{{ end }}
    <img
      class="mx-auto my-0 rounded-md"
      alt="{{ $altText }}"
      {{ if .Site.Params.enableImageLazyLoading | default true }}
        loading="lazy"
      {{ end }}
      {{ if findRE "^https?" $url.Scheme }}
        src="{{ $url.String }}"
      {{ else }}
        {{ $resource := "" }}
        {{ if $.Page.Resources.GetMatch ($url.String) }}
          {{ $resource = $.Page.Resources.GetMatch ($url.String) }}
        {{ else if resources.GetMatch ($url.String) }}
          {{ $resource = resources.Get ($url.String) }}
        {{ end }}
        {{ with $resource }}
          {{ if eq .MediaType.SubType "svg" }}
            src="{{ .RelPermalink }}"
          {{ else }}
              src="{{ .RelPermalink }}"
          {{ end }}
        {{ else }}
          src="{{ $url.String }}"
        {{ end }}
      {{ end }}
    />
    {{ with $href }}</a>{{ end }}
    {{ with $caption }}<figcaption style="text-align: center; margin-top: .8571429em; font-size: .875em">{{ . | markdownify }}</figcaption>{{ end }}
  </figure>
{{ end }}