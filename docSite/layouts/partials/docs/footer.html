<!-- Footer Start -->
<footer class="shadow py-3 d-print-none">
        <div class="row align-items-center" style="height: 90px">
            <div class="col">
                <div class="text-sm-start text-center mx-md-2">
                    <p class="mb-0">
                        {{ $yearToken := (cond (isset .Site.Params (lower "copyrightYearToken")) $.Site.Params.copyrightYearToken ":YEAR:") }}
                        {{ replace $.Site.Params.footer.copyright $yearToken (string (now.Format "2006")) | markdownify }}
                    </p>
                    <!--change-->
	                <p class="github-badge">
	                  <span class="badge-subject">云操作系统</span><span class="badge-value bg-blue"><a style="color:#fff" href="https://sealos.io/" target="_blank">Sealos</a></span>
                      <span class="badge-subject">云开发</span><span class="badge-value bg-brightgreen"><a style="color:#fff" href="https://laf.run" target="_blank">Laf</a></span>
	                  <span class="badge-subject">云原生存储</span><span class="badge-value bg-orange"><a style="color:#fff" href="https://github.com/labring/sealfs" target="_blank">Sealfs</a></span>
	                </p>
                    <div id="fixed-box">
                      <div class="feedback-btn-wrapper">
                        <a
                          href="https://fael3z0zfze.feishu.cn/share/base/form/shrcnRxj3utrzjywsom96Px4sud"
                          style="text-decoration: none"
                          target="_blank"
                        >
                          <button id="feedback-btn" title="Give feedback">
                            <svg class="inline w-5 h-5" viewBox="0 0 20 20" fill="currentColor">
                              <path
                                fill-rule="evenodd"
                                d="M18 13V5a2 2 0 00-2-2H4a2 2 0 00-2 2v8a2 2 0 002 2h3l3 3 3-3h3a2 2 0 002-2zM5 7a1 1 0 011-1h8a1 1 0 110 2H6a1 1 0 01-1-1zm1 3a1 1 0 100 2h3a1 1 0 100-2H6z"
                                clip-rule="evenodd"
                              ></path>
                            </svg>
                            <span _msttexthash="6039839" _msthash="295">咨询合作</span>
                          </button>
                        </a>
                      </div>
                    </div>
                    <!-- End -->
                </div>
            </div><!--end col-->
        </div><!--end row-->
</footer><!--end footer-->
<!-- End -->