<head>
    <!-- <script defer type="text/javascript" src="{{ "js/jsdelivr-auto-fallback.js" | absURL }}"></script> -->
    <meta charset="utf-8" />
    <title>
        {{- $url := replace .Permalink ( printf "%s" .Site.BaseURL) "" }}
        {{- if eq $url "/" }}
            {{- .Site.Title }}
        {{- else }}
            {{- if .Params.heading }}
                {{ .Params.heading }}
            {{ else }}
                {{- if eq .Title .Site.Title }}
                    {{- .Title }}
                {{- else }}
                    {{- .Title }} | {{ .Site.Params.docs.Title | default (.Site.Title) }}
                {{- end }}
            {{- end }}
        {{- end -}}
    </title>
    {{- if not hugo.IsProduction }}
    <meta name="robots" content="noindex">
    {{- end }}
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    {{- with .Description | default ($.Param "description") }}
    <meta name="description" content="{{ . }}">
    {{- end }}
    <meta name="keywords" content="Docume<PERSON>, <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>" />
    <meta name="author" content="<PERSON> - Lotus Labs" />
    <meta name="email" content="<EMAIL>" />
    <meta name="website" content="https://lotusdocs.dev" />
    <meta name="Version" content="v0.1.0" />
    <!-- favicon -->
    {{ block "favicon" . }}{{ partialCached (printf "%s/%s" ($.Scratch.Get "pathName") "head/favicon.html") . }}{{ end }}
    {{- partial (printf "%s/%s" ($.Scratch.Get "pathName") "head/opengraph") . }}
    {{- partial (printf "%s/%s" ($.Scratch.Get "pathName") "head/twitter_cards") . }}
    <!-- Dark Mode -->
    {{ if eq .Site.Params.docs.darkMode true -}}
        {{ $darkModeInit := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "js/darkmode-init.js") | js.Build | minify -}}
        <script>{{ $darkModeInit.Content | safeJS }}</script>
    {{ end -}}
    <!-- FlexSearch -->
    {{ if or (not (isset .Site.Params.flexsearch "enabled")) (eq .Site.Params.flexsearch.enabled true) -}}
        {{ if and (.Site.Params.docsearch.appID) (.Site.Params.docsearch.apiKey) -}}
        {{ else }}
            {{ $flexSearch := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "js/flexsearch.bundle.js") }}
            {{- if not .Site.IsServer }}
                {{ $flexSearch := $flexSearch | minify | fingerprint "sha384" }}
                <script type="text/javascript" src="{{ $flexSearch.Permalink }}" integrity="{{ $flexSearch.Data.Integrity }}" crossorigin="anonymous"></script>
                {{ else }}
                <script type="text/javascript" src="{{ $flexSearch.Permalink }}"></script>
            {{ end }}
        {{ end }}
    {{ end }}
    <!-- Google Fonts -->
    {{- partialCached "google-fonts" . }}
    <!-- Custom CSS -->
    {{- $options := dict "enableSourceMap" true }}
    {{- if hugo.IsProduction}}
        {{- $options := dict "enableSourceMap" false "outputStyle" "compressed" }}
    {{- end }}
    {{- $style := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "scss/style.scss") }}
    {{- $style = $style | resources.ExecuteAsTemplate (printf "/%s/%s" ($.Scratch.Get "pathName") "scss/style.scss") . | resources.ToCSS $options }}
    {{- if hugo.IsProduction }}
        {{- $style = $style | minify | fingerprint "sha384" }}
    {{- end -}}
    <link rel="stylesheet" href="{{ $style.RelPermalink }}" {{ if hugo.IsProduction }}integrity="{{ $style.Data.Integrity }}"{{ end -}} crossorigin="anonymous">
    <!-- Katex CSS -->
    {{- if .Params.katex -}}
    {{- $options := dict "enableSourceMap" true }}
    {{- if hugo.IsProduction}}
        {{- $options := dict "enableSourceMap" false "outputStyle" "compressed" }}
    {{- end -}}
    {{- $katexCSS := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "scss/katex.scss") }}
    {{- $katexCSS = $katexCSS | resources.ExecuteAsTemplate (printf "/%s/%s" ($.Scratch.Get "pathName") "scss/katex.scss") . | resources.ToCSS $options }}
    {{- if hugo.IsProduction }}
        {{- $katexCSS = $katexCSS | minify | fingerprint "sha384" }}
    {{- end -}}
    <link rel="stylesheet" href="{{ $katexCSS.RelPermalink }}" {{ if hugo.IsProduction }}integrity="{{ $katexCSS.Data.Integrity }}"{{ end -}} crossorigin="anonymous">
    {{- end -}}
    <!-- Katex JS -->
    {{- if .Params.katex -}}
    {{ $katex := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "js/katex.js") }}
    {{ $katexAutoRender := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "js/auto-render.js") }}
    {{ if hugo.IsProduction }}
        {{ $katex = $katex | minify | fingerprint "sha384" }}
        {{ $katexAutoRender = $katexAutoRender | minify | fingerprint "sha384" }}
    {{- end -}}
    <script src="{{ $katex.RelPermalink }}" {{ if hugo.IsProduction }}integrity="{{ $katex.Data.Integrity }}"{{ end -}} defer></script>
    <script src="{{ $katexAutoRender.RelPermalink }}" {{ if hugo.IsProduction }}integrity="{{ $katexAutoRender.Data.Integrity }}"{{ end -}} defer></script>
    {{ end -}}

    <!-- Katex Config -->
    {{ if .Params.katex }}
        {{- partialCached (printf "%s/%s" ($.Scratch.Get "pathName") "footer/katex.html") . -}}
    {{ end }}
    <!-- Plausible Analytics Config -->
    {{- if not .Site.IsServer }}
    {{ if and (.Site.Params.plausible.scriptURL | default "https://plausible.io") (.Site.Params.plausible.dataDomain) -}}
        {{- partialCached (printf "%s/%s" ($.Scratch.Get "pathName") "head/plausible") . }}
    {{- end -}}
    {{- end -}}
    <!-- Google Analytics v4 Config -->
    {{- if not .Site.IsServer }}
    {{- if .Site.GoogleAnalytics }}
        {{- template "_internal/google_analytics.html" . -}}
    {{- end -}}
    {{- end -}}
    <!-- change -->
    <link rel="preload" href="https://cdn.jsdelivr.net/npm/lxgw-wenkai-screen-webfont@1.1.0/style.css" as="style" />
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/lxgw-wenkai-screen-webfont@1.1.0/style.css" />
</head>