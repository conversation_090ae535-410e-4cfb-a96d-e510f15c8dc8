<script>
    window.addEventListener('DOMContentLoaded', function() {
        // DocSearch Config
        docsearch({
            container: '#docsearch',
            appId: '{{ .Site.Params.docsearch.appID }}',
            apiKey: '{{ .Site.Params.docsearch.apiKey }}',
            indexName: '{{ .Site.Params.docsearch.indexName }}',
            placeholder: '{{ i18n "search_title" }}',
            translations: {
                button: {
                    buttonText: '{{ i18n "search_title" }}',
                    buttonAriaLabel: '{{ i18n "search_title" }}',
                },
                modal: {
                    startScreen: {
                        noRecentSearchesText: '{{ i18n "search_no_recent_searches" }}',
                    },
                    footer: {
                        selectText: '{{ i18n "search_select" }}',
                        selectKeyAriaLabel: 'Enter key',
                        navigateText: '{{ i18n "search_navigate" }}',
                        navigateUpKeyAriaLabel: 'Arrow up',
                        navigateDownKeyAriaLabel: 'Arrow down',
                        closeText: '{{ i18n "search_close" }}',
                        closeKeyAriaLabel: 'Escape key',
                        searchByText: '{{ i18n "search_search_by" }}',
                    },
                    noResultsScreen: {
                        noResultsText: '{{ i18n "search_no_results" }}',
                        suggestedQueryText: '{{ i18n "search_try_search" }}',
                        reportMissingResultsText: 'Believe this query should return results?',
                        reportMissingResultsLinkText: 'Let us know.',
                    },
                },
            }
        });
    });
</script>