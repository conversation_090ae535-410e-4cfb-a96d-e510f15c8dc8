{{ $dayjs := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "js/dayjs.min.js") }}
{{ $relativeTime := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "js/relativeTime.min.js") }}
{{ $app := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "js/app.js") -}}

{{ $slice := slice $dayjs $relativeTime $app -}}

{{ if and (.Site.Params.docsearch.appID) (.Site.Params.docsearch.apiKey) -}}
    {{ $docsearch := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "js/docsearch.min.js") }}
    {{ $slice = $slice | append $docsearch -}}
{{ end }}

{{ if site.Params.docs.toc | default true }}
    {{ if eq .Site.Params.docs.scrollSpy true -}}
        {{ $simplescrollspy := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "js/simple-scrollspy.min.js") }}
        {{ $slice = $slice | append $simplescrollspy -}}
    {{ end -}}

    {{ if eq .Site.Params.docs.scrollSpy true -}}
        {{ $scrollspyScript := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "js/scrollspy-script.js") }}
        {{ $scrollspyScript := $scrollspyScript | js.Build -}}
        {{ $slice = $slice | append $scrollspyScript -}}
    {{ end -}}
{{ end -}}

{{ if site.Params.docs.tocMobile | default true }}
    {{ $tocmobilescrollspy := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "js/toc-mobile-scrollspy.js") }}
    {{ $slice = $slice | append $tocmobilescrollspy -}}
{{ end -}}

{{ if eq .Site.Params.docs.prism true -}}
    {{ $prism := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "js/prism.js") }}
    {{- $opts := dict
        "params" (dict "langPath" (urls.JoinPath .Site.BaseURL "docs/js/components/"))
    -}}
    {{ $prism := $prism | js.Build $opts -}}
    {{ $slice = $slice | append $prism -}}
{{ end -}}

<!-- Bootstrap JS -->
{{ $js := resources.Get (printf "/%s/%s" ($.Scratch.Get "pathName") "js/bootstrap.js") }}
{{ $params := dict }}
{{ $sourceMap := cond hugo.IsProduction "" "inline" }}
{{ $opts := dict "sourceMap" $sourceMap "minify" hugo.IsProduction "target" "es2018" "params" $params }}
{{ $js = $js | js.Build $opts }}
{{ if hugo.IsProduction }}
    {{ $js = $js | fingerprint "sha384" }}
{{ end }}
    <script src="{{ $js.RelPermalink }}" {{ if hugo.IsProduction }}integrity="{{ $js.Data.Integrity }}"{{ end -}} defer></script>

{{ $js := $slice | resources.Concat (printf "/%s/%s" ($.Scratch.Get "pathName") "js/bundle.js") -}}

{{- if not .Site.IsServer }}
    {{- $js := $js | minify | fingerprint "sha384" }}
    <script type="text/javascript" src="{{ $js.Permalink }}" integrity="{{ $js.Data.Integrity }}" crossorigin="anonymous" defer></script>
{{- else }}
    <script type="text/javascript" src="{{ $js.Permalink }}" defer></script>
{{- end }}

<!-- change -->
<script
  src="https://cdn.jsdelivr.net/npm/medium-zoom/dist/medium-zoom.min.js"
  crossorigin="anonymous"
  referrerpolicy="no-referrer"
></script>

<script>
  const images = Array.from(document.querySelectorAll('.medium-zoom-image'));
  images.forEach((img) => {
    mediumZoom(img, {
      margin: 0 /* The space outside the zoomed image */,
      scrollOffset: 40 /* The number of pixels to scroll to close the zoom */,
      container: null /* The viewport to render the zoom in */,
      template: null /* The template element to display on zoom */,
      background: "rgba(0, 0, 0, 0.8)"
    });
  });
</script>