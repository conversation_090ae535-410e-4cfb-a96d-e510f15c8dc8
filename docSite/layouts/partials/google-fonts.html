{{ if .Site.Params.google_fonts }}
    {{ $fonts := slice }}
    {{ range .Site.Params.google_fonts }}
        {{ $family := replace (index (.)  0) " " "+" }}
        {{ $weights := replace (index (.) 1) " " "" }}
        {{ $string := print $family ":" $weights }}
        {{ $fonts = $fonts | append $string }}
    {{ end }}
    {{ $url_part := (delimit $fonts "|") | safeHTMLAttr }}
    <link rel="preconnect" href="https://gstatic.loli.net/" />
    <link rel="preconnect" href="https://gstatic.loli.net/" crossorigin />
    <link {{ printf "href=\"https://fonts.loli.net/css?family=%s%s\"" $url_part "&display=block" | safeHTMLAttr }} rel="stylesheet">
{{ else}}
{{ end }}