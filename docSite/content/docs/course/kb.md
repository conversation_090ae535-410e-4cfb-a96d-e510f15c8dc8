---
title: " 打造高质量 AI 知识库(过期)"
description: " 利用 FastGPT 打造高质量 AI 知识库"
icon: "school"
draft: false
toc: true
weight: 300
---

## 前言

自从去年 12 月 ChatGPT 发布后，带动了新的一轮应用交互革命。尤其是 GPT-3.5 接口全面放开后，LLM 应用雨后春笋般快速涌现，但因为 GPT 的可控性、随机性和合规性等问题，很多应用场景都没法落地。

3 月时候，在 Twitter 上刷到一个老哥使用 GPT 训练自己的博客记录，并且成本非常低（比起 FT）。他给出了一个完整的流程图：

![向量搜索 GPT 流程图](/imgs/1.png)

看到这个推文后，我灵机一动，应用场景就十分清晰了。直接上手开干，在经过不到 1 个月时间，FastGPT 在原来多助手管理基础上，加入了向量搜索。于是便有了最早的一期视频：

{{< bilibili BV1Wo4y1p7i1 >}}

3 个月过去了，FastGPT 延续着早期的思路去完善和扩展，目前在向量搜索 + LLM 线性问答方面的功能基本上完成了。不过我们始终没有出一期关于如何构建知识库的教程，趁着 V4 在开发中，我们计划介绍一期《如何在 FastGPT 上构建高质量知识库》，以便大家更好的使用。

## FastGPT 知识库完整逻辑

在正式构建知识库前，我们先来了解下 FastGPT 是如何进行知识库检索的。首先了解几个基本概念：

1. 向量：将人类直观的语言（文字、图片、视频等）转成计算机可识别的语言（数组）。
2. 向量相似度：两个向量之间可以进行计算，得到一个相似度，即代表：两个语言相似的程度。
3. 语言大模型的一些特点：上下文理解、总结和推理。

结合上述 3 个概念，便有了 “向量搜索 + 大模型 = 知识库问答” 的公式。下图是 FastGPT V3 中知识库问答功能的完整逻辑：

![向量搜索 GPT 流程图](/imgs/2.png)

与大部分其他知识库问答产品不一样的是， FastGPT 采用了 QA 问答对进行存储，而不是仅进行 chunk（文本分块）处理。目的是为了减少向量化内容的长度，让向量能更好的表达文本的含义，从而提高搜索精准度。
此外 FastGPT 还提供了搜索测试和对话测试两种途径对数据进行调整，从而方便用户调整自己的数据。根据上述流程和方式，我们以构建一个 FastGPT 常见问题机器人为例，展示如何构建一个高质量的 AI 知识库。

## 构建知识库应用

首先，先创建一个 FastGPT 常见问题知识库

![创建知识库应用](/imgs/3.png)

### 通过 QA 拆分，获取基础知识

我们先直接把 FastGPT Git 上一些已有文档，进行 QA 拆分，从而获取一些 FastGPT 基础的知识。下面是 README 例子。

![QA 拆分示意图](/imgs/4.png)

![](/imgs/5.png)

### 修正 QA

通过 README 我们一共得到了 11 组数据，整体的质量还是不错的，图片和链接都提取出来了。不过最后一个知识点出现了一些截断，我们需要手动的修正一下。

此外，我们观察到第一列第三个知识点。这个知识点是介绍了 FastGPT 一些资源链接，但是 QA 拆分将答案放置在了 A 中，但通常来说用户的提问并不会直接问“有哪些链接”，通常会问：“部署教程”，“问题文档”之类的。因此，我们需要将这个知识点进行简单的一个处理，如下图：

![手动修改知识库数据](/imgs/6.png)

我们先来创建一个应用，看看效果如何。 首先需要去创建一个应用，并且在知识库中关联相关的知识库。另外还需要在配置页的提示词中，告诉 GPT：“知识库的范围”。

![](/imgs/7.png)

![README QA 拆分后效果](/imgs/8.png)

整体的效果还是不错的，链接和对应的图片都可以顺利的展示。

### 录入社区常见问题

接着，我们再把 FastGPT 常见问题的文档导入，由于平时整理不当，我们只能手动的录入对应的问答。

![手动录入知识库结果](/imgs/9.png)

导入结果如上图。可以看到，我们均采用的是问答对的格式，而不是粗略的直接导入。目的就是为了模拟用户问题，进一步的提高向量搜索的匹配效果。可以为同一个问题设置多种问法，效果更佳。
FastGPT 还提供了 openapi 功能，你可以在本地对特殊格式的文件进行处理后，再上传到 FastGPT，具体可以参考：[FastGPT Api Docs](https://doc.fastgpt.in/docs/development/openapi)

## 知识库微调和参数调整

FastGPT 提供了搜索测试和对话测试两种途径对知识库进行微调，我们先来使用搜索测试对知识库进行调整。我们建议你提前收集一些用户问题进行测试，根据预期效果进行跳转。可以先进行搜索测试调整，判断知识点是否合理。

### 搜索测试

![搜索测试作用](/imgs/10.png)

你可能会遇到下面这种情况，由于“知识库”这个关键词导致一些无关内容的相似度也被搜索进去，此时就需要给第四条记录也增加一个“知识库”关键词，从而去提高它的相似度。

![搜索测试结果](/imgs/11.png)

![优化后的搜索测试结果](/imgs/12.png)

### 提示词设置

提示词的作用是引导模型对话的方向。在设置提示词时，遵守 2 个原则：

1. 告诉 Gpt 回答什么方面内容。
2. 给知识库一个基本描述，从而让 Gpt 更好的判断用户的问题是否属于知识库范围。

![提示词设置](/imgs/13.png)

### 更好的限定模型聊天范围

首先，你可以通过调整知识库搜索时的相似度和最大搜索数量，实现从知识库层面限制聊天范围。通常我们可以设置相似度为 0.82，并设置空搜索回复内容。这意味着，如果用户的问题无法在知识库中匹配时，会直接回复预设的内容。

![搜索参数设置](/imgs/14.png)

![空搜索控制效果](/imgs/15.png)

由于 openai 向量模型并不是针对中文，所以当问题中有一些知识库内容的关键词时，相似度
会较高，此时无法从知识库层面进行限定。需要通过限定词进行调整，例如：

> 我的问题如果不是关于 FastGPT 的，请直接回复：“我不确定”。你仅需要回答知识库中的内容，不在其中的内容，不需要回答。

效果如下：

![限定词效果](/imgs/16.png)

当然，gpt35 在一定情况下依然是不可控的。

### 通过对话调整知识库

与搜索测试类似，你可以直接在对话页里，点击“引用”，来随时修改知识库内容。

![查看答案引用](/imgs/17.png)

## 总结

1. 向量搜索是一种可以比较文本相似度的技术。
2. 大模型具有总结和推理能力，可以从给定的文本中回答问题。
3. 最有效的知识库构建方式是 QA 和手动构建。
4. Q 的长度不宜过长。
5. 需要调整提示词，来引导模型回答知识库内容。
6. 可以通过调整搜索相似度、最大搜索数量和限定词来控制模型回复的范围。