---
title: '商业版'
description: 'FastGPT 商业版相关说明'
icon: 'shopping_cart'
draft: false
toc: true
weight: 1001
---

## 简介

FastGPT 商业版是基于 FastGPT 开源版的增强版本，增加了一些独有的功能。只需安装一个商业版镜像，并在开源版基础上填写对应的内网地址，即可快速使用商业版。

## 功能差异

{{< table "table-hover table-striped-columns" >}}
| | 开源版 | 商业版 | 线上版 |
| ---- | ---- | ---- | ---- |
| 应用管理与高级编排 | ✅ | ✅ | ✅ |
| 文档知识库 | ✅ | ✅ | ✅ |
| 外部使用 | ✅ | ✅ | ✅ |
| 自定义版权信息 | ❌ | ✅ | 设计中 |
| 多租户与支付 | ❌ | ✅ | ✅ |
| 团队空间 | ❌ | ✅ | ✅ |
| 应用发布安全配置 | ❌ | ✅ | ✅ |
| 内容审核 | ❌ | ✅ | ✅ |
| web站点同步 | ❌ | ✅ | ✅ |
| 管理后台 | ❌ | ✅ | ✅ |
| 增强训练模式 | ❌ | ✅ | ✅ |
| 图片知识库 | ❌ | 设计中 | 设计中 |
| 自动规划召回 | ❌ | 设计中 | 设计中 |
| 对话日志运营分析 | ❌ | 设计中 | 设计中 |
| 完整商业授权 | ❌ | ✅ | ✅ |
{{< /table >}}

## 商业版软件价格

FastGPT 商业版软件根据不同的部署方式，分为 3 类收费模式。下面列举各种部署方式一些常规内容，如仍有问题，可[联系咨询](https://fael3z0zfze.feishu.cn/share/base/form/shrcnRxj3utrzjywsom96Px4sud)

**共有服务**

1. Saas 商业授权许可 - 在商业版有效期内，可提供任意形式的商业服务。
2. 首次免费帮助部署。
3. 优先问题工单处理。

**特有服务**

{{< table "table-hover table-striped-columns" >}}
| 部署方式 | 特有服务 | 上线时长 | 标品价格 |
| ---- | ---- | ---- | ---- |
| Sealos全托管 |  1. 有效期内免费升级。<br>2. 免运维服务&数据库。 |  半天  | 5000元起/月（3个月起）<br>或<br>50000元起/年  |
| 自有服务器部署 |  1. 6个版本的升级服务。 | 14天内 |  具体价格可[联系咨询](https://fael3z0zfze.feishu.cn/share/base/form/shrcnRxj3utrzjywsom96Px4sud) |
{{< /table >}}

{{% alert icon="🤖 " context="success" %}}
- 6个版本的升级服务不是指只能用 6 个版本，而是指依赖 FastGPT 团队提供的升级服务。大部分时候，建议自行升级，也不麻烦。
- 全托管版本适合技术人员紧缺的团队，仅需关注业务推动，无需关心服务是否正常运行。
- 自有服务器部署版可以完全部署在自己服务器中。
- 单机版适合中小团队对内提供服务，需要自己维护数据库备份等。
- 高可用版适合对外提供在线服务，包含可视化监控、多副本、负载均衡、数据库自动备份等生产环境的基础设施。
{{% /alert %}}


## 技术支持

### 应用定制

根据需求，定制实现某个需求的编排功能，最终会交付一个应用编排。可根据实际情况商讨。

### 技术服务费（定开、维护、迁移、三方接入等）

2000 ~ 3000元/人/天

### 更新升级费用

大部分更新升级，重新拉镜像，然后执行一下初始化脚本就可以了，不需要执行额外操作。

跨版本更新或复杂更新可参考文档自行更新；或付费支持，标准与技术服务费一致。

## 联系方式

请填写[咨询问卷](https://fael3z0zfze.feishu.cn/share/base/form/shrcnRxj3utrzjywsom96Px4sud)，我们会尽快与您联系。

## QA

1. 如何交付？

   完整版应用 = 开源版镜像 + 商业版镜像

   我们会提供一个商业版镜像给你使用，该镜像需要一个 License 启动。

2. 二次开发如何操作？

   可自行修改开源版代码进行二次开发，不支持修改商业版镜像。


## Sealos 费用

Sealos 云服务属于按量计费，下面是它的价格表：

![](/imgs/sealos_price.jpg)
