---
title: "AI 对话"
description: "FastGPT AI 对话模块介绍"
icon: "chat"
draft: false
toc: true
weight: 351
---

## 特点

- 可重复添加
- 有外部输入
- 有静态配置
- 触发执行
- 核心模块

![](/imgs/aichat.png)

## 参数说明

## AI模型

可以通过 [config.json](/docs/development/configuration/) 配置可选的对话模型，通过 [one-api](/docs/development/one-api/) 来实现多模型接入。

点击AI模型后，可以配置模型的相关参数。

![](/imgs/aichat02.png)

![](/imgs/aichat2.png)



{{% alert icon="🍅" context="success" %}}
具体配置参数介绍可以参考: [AI参数配置说明](/docs/course/ai_settings)
{{% /alert %}}