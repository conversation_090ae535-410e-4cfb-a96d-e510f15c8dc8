---
title: '升级到 V4.2.1'
description: 'FastGPT 从旧版本升级到 V4.2.1 操作指南'
icon: 'upgrade'
draft: false
toc: true
weight: 847
---

私有部署，如果添加了配置文件，需要在配置文件中修改 `VectorModels` 字段。增加 defaultToken 和 maxToken，分别对应直接分段时的默认 token 数量和该模型支持的 token 上限 (通常不建议超过 3000)

```json
"VectorModels": [
    {
      "model": "text-embedding-ada-002",
      "name": "Embedding-2",
      "price": 0,
      "defaultToken": 500,
      "maxToken": 3000
    }
]
```

改动目的是，我们认为不需要留有选择余地，选择一个最合适的模型去进行任务即可。
