---
title: 'V4.6(需要初始化)'
description: 'FastGPT V4.6 更新'
icon: 'upgrade'
draft: false
toc: true
weight: 836
---

**V4.6 版本加入了简单的团队功能，可以邀请其他用户进来管理资源。该版本升级后无法执行旧的升级脚本，且无法回退。**

## 1。更新镜像并变更配置文件

更新镜像至 latest 或者 v4.6 版本。商业版镜像更新至 V0.2.1

最新配置可参考：[V46 版本最新 config.json](/docs/development/configuration)，商业镜像配置文件也更新，参考最新的飞书文档。


## 2。执行初始化 API

发起 2 个 HTTP 请求 ({{rootkey}} 替换成环境变量里的 `rootkey`，{{host}} 替换成自己域名)

**该初始化接口可能速度很慢，返回超时不用管，注意看日志即可，需要注意的是，需确保 initv46 成功后，在执行 initv46-2**

1. https://xxxxx/api/admin/initv46

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv46' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

2. https://xxxxx/api/admin/initv46-2

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv46-2' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

初始化内容：
1。创建默认团队
2。初始化 Mongo 所有资源的团队字段
3。初始化 Pg 的字段
4。初始化 Mongo Data


## V4.6 功能介绍

1. 新增 - 团队空间
2. 新增 - 多路向量 (多个向量映射一组数据)
3. 新增 - tts 语音
4. 新增 - 支持知识库配置文本预处理模型
5. 线上环境新增 - ReRank 向量召回，提高召回精度
6. 优化 - 知识库导出，可直接触发流下载，无需等待转圈圈

## 4.6 缺陷修复

旧的 4.6 版本由于缺少一个字段，导致文件导入时知识库数据无法显示，可执行下面的脚本：

https://xxxxx/api/admin/initv46-fix

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv46-fix' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```