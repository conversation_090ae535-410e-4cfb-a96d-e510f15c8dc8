---
title: 'V4.5.1(需进行初始化)'
description: 'FastGPT V4.5.1 更新'
icon: 'upgrade'
draft: false
toc: true
weight: 838
---

## 执行初始化 API

发起 1 个 HTTP 请求（{{rootkey}} 替换成环境变量里的`rootkey`，{{host}}替换成自己域名）

1. https://xxxxx/api/admin/initv451

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv451' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

初始化内容：
1. rename 数据库字段
2. 初始化 Mongo APP 表中知识库的相关字段
3. 初始化 PG 和 Mongo 的内容，为每个文件创建一个集合（存储 Mongo 中），并反馈赋值给 PG。

**该初始化接口可能速度很慢，返回超时不用管，注意看日志即可**

## 功能介绍

### Fast GPT V4.5.1

1. 新增知识库文件夹管理
2. 修复了 openai4.x sdk 无法兼容 oneapi 的智谱和阿里的接口。
3. 修复部分模块无法触发完成事件