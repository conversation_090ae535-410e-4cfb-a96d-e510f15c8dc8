---
title: '升级到 V4.4.1(需要初始化)'
description: 'FastGPT 从旧版本升级到 V4.4.1 操作指南'
icon: 'upgrade'
draft: false
toc: true
weight: 844
---

## 执行初始化 API

发起 1 个 HTTP 请求（记得携带 `headers.rootkey`，这个值是环境变量里的）

1. https://xxxxx/api/admin/initv441

```bash
curl --location --request POST 'https://{{host}}/api/admin/initv441' \
--header 'rootkey: {{rootkey}}' \
--header 'Content-Type: application/json'
```

会给初始化 Mongo 的 dataset.files，将所有数据设置为可用。

