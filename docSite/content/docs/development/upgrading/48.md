---
title: 'V4.8(进行中)'
description: 'FastGPT V4.8 更新说明'
icon: 'upgrade'
draft: false
toc: true
weight: 824
---

## 新工作流

FastGPT workflow V2上线，支持更加简洁的工作流模式。

**由于工作流差异较大，需要手动重新构建。**

给应用和插件增加了 version 的字段，用于标识是旧工作流还是新工作流。当你更新 4.8 后，保存和新建的工作流均为新版，旧版工作流会有一个重置的弹窗提示。并且，如果是通过 API 和 分享链接 调用的工作流，仍可以正常使用，直到你下次保存它们。

## V4.8 更新说明

1. 重构 - 工作流
2. 新增 - 工作流 Debug 模式，可以调试单个节点或者逐步调试工作流。
3. 新增 - 定时执行应用。可轻松实现定时任务。
4. 新增 - 插件自定义输入优化，可以渲染输入组件。
6. 优化 - 工作流连线，可以四向连接，方便构建循环工作流。
7. 优化 - 工作流上下文传递，性能🚀。
8. 优化 - 简易模式，更新配置后自动更新调试框内容，无需保存。
9. 优化 - worker进程管理，并将计算 Token 任务分配给 worker 进程。