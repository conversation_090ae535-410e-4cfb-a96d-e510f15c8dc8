---
title: 'V4.6.5（需要改配置文件）'
description: 'FastGPT V4.6.5'
icon: 'upgrade'
draft: false
toc: true
weight: 831
---

## 配置文件变更

由于 openai 已开始弃用 function call，改为 toolChoice。FastGPT 同步的修改了对于的配置和调用方式，需要对配置文件做一些修改：

[点击查看最新的配置文件](/docs/development/configuration/)

1. 主要是修改模型的`functionCall`字段，改成`toolChoice`即可。设置为`true`的模型，会默认走 openai 的 tools 模式；未设置或设置为`false`的，会走提示词生成模式。

问题优化模型与内容提取模型使用同一组配置。

2. 增加 `"ReRankModels": []`

## V4.6.5 功能介绍

1. 新增 - [问题优化模块](/docs/workflow/modules/coreferenceresolution/)
2. 新增 - [文本编辑模块](/docs/workflow/modules/text_editor/)
3. 新增 - [判断器模块](/docs/workflow/modules/tfswitch/)
4. 新增 - [自定义反馈模块](/docs/workflow/modules/custom_feedback/)
5. 新增 - 【内容提取】模块支持选择模型，以及字段枚举
6. 优化 - docx读取，兼容表格（表格转markdown）
7. 优化 - 高级编排连接线交互
8. 优化 - 由于 html2md 导致的 cpu密集计算，阻断线程问题
9. 修复 - 高级编排提示词提取描述

