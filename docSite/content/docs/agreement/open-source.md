---
title: '开源协议'
description: ' FastGPT 开源许可证'
icon: 'verified_user'
draft: false
toc: true
weight: 1210
---

FastGPT 项目在 Apache License 2.0 许可下开源，同时包含以下附加条件：

+ FastGPT 允许被用于商业化，例如作为其他应用的“后端即服务”使用，或者作为应用开发平台提供给企业。然而，当满足以下条件时，必须联系作者获得商业许可：
   
   + 多租户 SaaS 服务：除非获得 FastGPT 的明确书面授权，否则不得使用 fastgpt.in 的源码来运营与 fastgpt.in 服务类似的多租户 SaaS 服务。
   + LOGO 及版权信息：在使用 FastGPT 的过程中，不得移除或修改 FastGPT 控制台内的 LOGO 或版权信息。

   请通过电子邮件 <EMAIL> 联系我们咨询许可事宜。

+ 作为贡献者，你必须同意将你贡献的代码用于以下用途：
  
   + 生产者有权将开源协议调整为更严格或更宽松的形式。
   + 可用于商业目的，例如 FastGPT 的云服务。 

除此之外，所有其他权利和限制均遵循 Apache License 2.0。如果你需要更多详细信息，可以参考 Apache License 2.0 的完整版本。本产品的交互设计受到外观专利保护。© 2023 Sealos.