:root {
    --code-bg: rgba(0, 0, 0, 0.03);
	--code-color: rgba(14, 116, 144, 0.95);
	--inline-code-border: 0.5px solid var(--gray-400);

}

[data-dark-mode] {
    --code-bg: hsla(0, 2%, 14%, 1);
	--code-color: #f3f4f6ed;
	--inline-code-border: 0.5px solid var(--gray-600);
}

#content {
	font-family: JetBrains Mono, LXGW WenKai Screen, -apple-system, BlinkMacSystemFont, "Segoe UI", "Roboto", "Helvetica Neue", "Ubuntu";
}

@media (min-width: 768px) {
    .docs-content h1 {
    	font-size: 2.4rem;
    }

	.docs-content .main-content h2 {
		margin-top: 2rem !important;
		margin-bottom: 1rem !important;
		font-size: 1.9rem;
	}
	
	.docs-content .main-content h3 {
		margin-top: 1.6rem !important;
		margin-bottom: 0.6rem !important;
		font-size: 1.6rem;
	}

	.docs-content .main-content h4 {
		font-size: 1.4rem;
	}
	
	.docs-content .main-content ol, .docs-content .main-content ul {
		margin-top: 0.5rem;
		margin-bottom: 0.5rem;
	}
	
	.docs-content .main-content ol > li {
		margin-left: -1.8rem;
	}
}

.docs-content .main-content ol > li::before {
    border-radius: 9999px;
}

.docs-content .main-content img, .docs-content .main-content  svg:not(.gitinfo svg):not(a svg) {
    max-width: 80% !important;
    height: auto;
    display: block !important;
    margin: 0 auto !important;
    border-radius: .25rem;
}

div.code-toolbar {
    padding-top: 1.95rem !important;
}

.docs-content .main-content pre code::before {
    background: #fc625d;
    border-radius: 50%;
    box-shadow: 20px 0 #fdbc40, 40px 0 #35cd4b;
    content: ' ';
    height: 12px;
    left: 12px;
    margin-top: -21px;
    position: absolute;
    width: 12px;
    z-index: 1;
}

.docs-content .main-content pre code {
	padding: 0 2.5rem 1.25rem .9rem;
  }

.docs-content .main-content code {
	font-size: .875em;
	padding: 1px 2px;
	background: var(--code-bg);
	border: var(--inline-code-border);
	padding-top: 3px;
	padding-bottom: 3px;
	padding-left: 5px;
	padding-right: 5px;
	border-radius: .25rem;
	color: var(--code-color);
}

li p {
    margin-top: 1rem !important;
    margin-bottom: 1rem;
}

.docs-content .main-content ul > li {
    margin-top: .3rem !important;
    margin-bottom: .3rem;
}

footer {
	height: 118px !important;
}

/*
footer a:hover {
    text-decoration: none !important;
}
*/

.medium-zoom-overlay,
.medium-zoom-image--opened {
  z-index: 1999;
}

/* 徽章样式 */
.github-badge {
	display: inline-block;
	border-radius: 4px;
	text-shadow: none;
	font-size: 12px;
	color: #fff;
	line-height: 15px;
    margin-bottom: 5px;
    margin-top: 5px;
}
.github-badge .badge-subject {
	display: inline-block;
	background-color: #4D4D4D;
	padding: 4px 4px 4px 6px;
	border-top-left-radius: 4px;
	border-bottom-left-radius: 4px;
}
.github-badge .badge-value {
	display: inline-block;
	padding: 4px 6px 4px 4px;
	border-top-right-radius: 4px;
	border-bottom-right-radius: 4px;
}
.github-badge .bg-brightgreen {
	background-color: #4DC820 !important;
}
.github-badge .bg-orange {
	background-color: #FFA500 !important;
}
.github-badge .bg-yellow {
	background-color: #D8B024 !important;
}
.github-badge .bg-blueviolet {
	background-color: #8833D7 !important;
}
.github-badge .bg-pink {
	background-color: #F26BAE !important;
}
.github-badge .bg-red {
	background-color: #e05d44 !important;
}
.github-badge .bg-blue {
	background-color: #007EC6 !important;
}
.github-badge .bg-lightgrey {
	background-color: #9F9F9F !important;
}
.github-badge .bg-grey, .github-badge .bg-gray {
	background-color: #555 !important;
}
.github-badge .bg-lightgrey, .github-badge .bg-lightgray {
	background-color: #9f9f9f !important;
}

#fixed-box {
	position: fixed;
	z-index: 9999;
}

@media (max-width: 600px) {
	#fixed-box {
		display: none
	}
}

.feedback-btn-wrapper {
	position: fixed;
	z-index: 1000;
	bottom: 0;
	left: 0;
	margin: 2rem;
}
#feedback-btn {
	height: 30px;
	display: flex;
	align-items: center;
	padding: 1.2rem 0.7rem;
	border-radius: 0.4rem;
	cursor: pointer;
	box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1),
	  0 4px 6px -2px rgba(0, 0, 0, 0.05);
	user-select: none;
	border: 0;
	outline: 0;
	color: #fff;
	background-color: #4d698e;
	transition: filter 0.4s ease;
}

#feedback-btn svg {
	width: 1.25rem;
	height: 1.25rem;
}

#feedback-btn span {
	font-weight: 700;
	font-size: 1rem;
	margin-left: 0.5rem;
}