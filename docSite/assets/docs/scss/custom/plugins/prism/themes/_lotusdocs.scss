 /**
 * Lotus Docs theme
 *
 * Adapted from a theme based on:
 * https://github.com/chriske<PERSON>on/tomorrow-theme
 *
 * <AUTHOR> <github.com/colinwilson>
 * @version 1.0
 */

 :root {
    --prism-code-bg: #faf9f8;
    --prism-code-scrollbar-thumb-color: var(--gray-400);
    --prism-color: #333;
    --prism-bg: #f0f0f0;
    --prism-highlight-bg: var(--blue-200);
    --prism-copy-bg: var(--gray-500);
    --prism-copy-hover-bg: var(--gray-700);
    --prism-copy-success-bg: var(--emerald-500);
    --prism-token-punctuation: #666;
    --prism-token-deleted: #2b6cb0;
    --prism-token-function-name: #3182bd;
    --prism-token-function: #c53030;
    --prism-token-number: var(--cardinal-600);
    --prism-token-symbol: #333;
    --prism-token-builtin: #1a202c;
    --prism-token-regex: #2f855a;
    --prism-token-variable: var(--yellow-700);
    --prism-token-url: #4fd1c5;
    --prism-token-inserted: #38a169;
}

[data-dark-mode] {
    --prism-code-bg: var(--gray-900);
    --prism-code-scrollbar-thumb-color: var(--gray-600);
    --prism-color: #f5fbff;
    --prism-bg: #32325d;
    --prism-highlight-bg: var(--blue-400);
    --prism-copy-bg: var(--gray-400);
    --prism-copy-hover-bg: var(--white);
    --prism-copy-success-bg: var(--emerald-200);
    --prism-token-punctuation: #ccc;
    --prism-token-deleted: #7fd3ed;
    --prism-token-function-name: #6196cc;
    --prism-token-function: #fda3f3;
    --prism-token-number: var(--cardinal-200);
    --prism-token-symbol: #ffffff;
    --prism-token-builtin: #a4cdfe;
    --prism-token-regex: #7ec699;
    --prism-token-variable: var(--yellow-100);
    --prism-token-url: #67cdcc;
    --prism-token-inserted: green;
}

 code[class*="language-"],
 pre[class*="language-"] {
     color: var(--prism-color) !important;
     background: var(--prism-code-bg) !important;
 }

/* Code blocks */
pre[class*="language-"] {
    // padding: 1em;
    // margin: .5em 0;
    overflow: auto;
    border-radius: 0 0 4px 4px;
}

:not(pre) > code[class*="language-"],
pre[class*="language-"] {
    background: var(--prism-bg);
}

/* Inline code */
:not(pre) > code[class*="language-"] {
    padding: .1em;
    border-radius: .3em;
    white-space: normal;
}

.line-highlight:before,
.line-highlight[data-end]:after {
    background-color: var(--prism-highlight-bg);
}

[data-copy-state="copy"] span:empty::before {
    background-color: var(--prism-copy-bg);
}

[data-copy-state="copy"] span:empty:hover::before {
    background-color: var(--prism-copy-hover-bg);
}

[data-copy-state="copy-success"] span:empty::before {
    background-color: var(--prism-copy-success-bg);
}

.token.comment,
.token.block-comment,
.token.prolog,
.token.doctype,
.token.cdata {
    color: #999;
}

.token.punctuation {
    color: var(--prism-token-punctuation);
}

.token.tag,
.token.attr-name,
.token.namespace,
.token.deleted {
    color: var(--prism-token-deleted);
}

.token.function-name {
    color: var(--prism-token-function-name);
}

.token.boolean,
.token.function {
    color: var(--prism-token-function);
}

.token.number {
    color: var(--prism-token-number);
}

.token.property,
.token.class-name,
.token.constant,
.token.symbol {
    color: var(--prism-token-symbol);
    font-weight: 700;
}

.token.selector,
.token.important,
.token.atrule,
.token.keyword,
.token.builtin {
    color: var(--prism-token-builtin);
    font-weight: 700;
}

.token.string,
.token.char,
.token.attr-value,
.token.regex {
    color: var(--prism-token-regex);
}

.token.variable {
    color: var(--prism-token-variable);
}

.token.operator,
.token.entity,
.token.url {
    color: var(--prism-token-url);
}

.token.important,
.token.bold {
    font-weight: bold;
}
.token.italic {
    font-style: italic;
}

.token.entity {
    cursor: help;
}

.token.inserted {
    color: var(--prism-token-inserted);
}