/* Template Name: Lotus Docs
   Author: <PERSON>
   E-mail: <EMAIL>
   Created: October 2022
   Version: 1.2.0
   File Description: Main CSS file for Lotus Docs
*/

// Custom Font Variables
$font-family-secondary:  {{ .Site.Params.secondary_font | default "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Ubuntu'" }};
$font-family-sans-serif: {{ .Site.Params.sans_serif_font | default "-apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', 'Ubuntu'" }};
$font-family-monospace:  {{ .Site.Params.mono_font | default "SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace" }};

// Code Padding Variables
$code-block-padding-top: {{ if eq .Site.Params.docs.prism true -}}0{{ else }}1.25rem 0 0 0{{ end }};

// Icon Fonts
@import "custom/plugins/icons/google-material";

// Core files
@import "../../scss/bootstrap/functions";
@import "../../scss/bootstrap/variables";
@import {{ printf "'%s%s'" "custom/colors/" (.Site.Params.docs.themeColor | default "blue") }}; // current theme color
@import "../../scss/bootstrap/mixins";
@import "../../scss/bootstrap/bootstrap";
@import "variables";

{{ if and (.Site.Params.docsearch.appID) (.Site.Params.docsearch.apiKey) -}}
@import "custom/plugins/docsearch/style";
{{ end }}

// Structure
@import "custom/structure/general";
@import "custom/structure/content";
@import "custom/structure/sidebar";
@import "custom/structure/doc-nav";
@import "custom/structure/toc";
@import "custom/structure/footer";

// Components
@import "custom/components/buttons";
@import "custom/components/modal";
@import "custom/components/breadcrumb";
@import "custom/components/badge";
@import "custom/components/backgrounds";
@import "custom/components/alerts";
@import "custom/components/card";
@import "custom/components/forms";
@import "custom/components/table";
@import "custom/components/tabs";
@import "custom/components/tooltip";

// Pages
@import "custom/pages/features";
@import "custom/pages/helper";

// Plugins

// Prism / Chroma
{{- if eq .Site.Params.docs.prism true }}
@import {{ printf "'%s%s'" "custom/plugins/prism/themes/" (.Site.Params.docs.prismTheme | default "lotusdocs") }}; // current prism theme
@import "custom/plugins/prism/prism";
{{- else }}
@import "custom/plugins/chroma/default";
{{- end -}}

// FlexSearch
{{ if or (not (isset .Site.Params.flexsearch "enabled")) (eq .Site.Params.flexsearch.enabled true) -}}@import "custom/plugins/flexsearch/flexsearch";{{ end }}

// Feedback Widget
{{ if .Site.Params.feedback.enabled | default false -}}@import "custom/plugins/feedback/feedback";{{ end}}

// Mermaid
@import "custom/plugins/mermaid/mermaid";

// change
@import "custom/pages/custom";