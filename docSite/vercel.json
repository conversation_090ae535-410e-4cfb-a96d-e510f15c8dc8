{"redirects": [{"source": "/", "destination": "/docs"}], "headers": [{"source": "/(.*)", "headers": [{"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "SAMEORIGIN"}, {"key": "X-XSS-Protection", "value": "1; mode=block"}, {"key": "Referrer-Policy", "value": "strict-origin"}, {"key": "Permissions-Policy", "value": "geolocation=(self), microphone=()"}]}, {"source": "/docs/fonts/:all*(woff2)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}], "trailingSlash": true, "github": {"enabled": false}}