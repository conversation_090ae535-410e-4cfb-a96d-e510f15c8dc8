name: Build FastGPT images in Personal warehouse
on:
  workflow_dispatch:
  push:
    paths:
      - 'projects/app/**'
      - 'packages/**'
    branches:
      - 'main'
jobs:
  build-fastgpt-images:
    runs-on: ubuntu-20.04
    if: github.repository != 'labring/FastGPT'
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2
        with:
          driver-opts: network=host
      - name: Cache Docker layers
        uses: actions/cache@v3
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-
      - name: Login to GitHub Container Registry
        uses: docker/login-action@v2
        with:
          registry: ghcr.io
          username: ${{ github.repository_owner }}
          password: ${{ secrets.GH_PAT }}
      - name: Set DOCKER_REPO_TAGGED based on branch or tag
        run: |
          echo "DOCKER_REPO_TAGGED=ghcr.io/${{ github.repository_owner }}/fastgpt:latest" >> $GITHUB_ENV
      - name: Build and publish image for main branch or tag push event
        env:
          DOCKER_REPO_TAGGED: ${{ env.DOCKER_REPO_TAGGED }}
        run: |
          docker buildx build \
          --build-arg name=app \
          --label "org.opencontainers.image.source=https://github.com/${{ github.repository_owner }}/FastGPT" \
          --label "org.opencontainers.image.description=fastgpt image" \
          --push \
          --cache-from=type=local,src=/tmp/.buildx-cache \
          --cache-to=type=local,dest=/tmp/.buildx-cache \
          -t ${DOCKER_REPO_TAGGED} \
          -f Dockerfile \
          .
