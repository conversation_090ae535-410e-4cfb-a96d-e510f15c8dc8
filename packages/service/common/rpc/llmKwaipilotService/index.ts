import { RPC, GRPCProtocol, KessRegistry, Consumer } from '@infra-node/rpc';
import { IConsumer } from './proto/llm_server_grpc_pb';
import { IS_PREONLINE, IS_OVERSEA } from '@fastgpt/global/core/chat/constants';
import { createLogger } from '@fastgpt/global/common/util/logger';

const logger = createLogger('llm-service');
const TIMEOUT = 20_000;

const GRPC_JAVA_SERVICE = IS_PREONLINE
  ? 'pmo-llm-server-api.llm-kwaipilot-service-test'
  : IS_OVERSEA
    ? 'pmo-llm-server-api-overseas.llm-kwaipilot-service'
    : 'pmo-llm-server-api.llm-kwaipilot-service';

logger.info(`current env: ${IS_PREONLINE ? 'pre' : 'prod'}, service: ${GRPC_JAVA_SERVICE}`);

const registry = new KessRegistry({
  balance: 'round-robin',
  serviceName: GRPC_JAVA_SERVICE
});

const protocol = new GRPCProtocol({
  methodLowerCamelCase: true,
  protoLoader: {
    enums: Number,
    oneofs: false
  },
  proto: require('./proto/llm_server_grpc_pb')
});

let consumer: Consumer<IConsumer> | null = null;
export async function getLLMServiceConsumer() {
  if (!consumer) {
    consumer = await RPC.createConsumer<IConsumer>({
      registry,
      protocol,
      timeout: TIMEOUT
    });
  }
  return consumer;
}

export * from './proto/llm_server_pb';
