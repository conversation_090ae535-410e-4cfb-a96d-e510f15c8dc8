// source: llm_server.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.kwaipilot.AgentModel', null, global);
goog.exportSymbol('proto.kwaipilot.AgentModelListQuery', null, global);
goog.exportSymbol('proto.kwaipilot.AgentModelListResponse', null, global);
goog.exportSymbol('proto.kwaipilot.ChatFile', null, global);
goog.exportSymbol('proto.kwaipilot.ChatMessage', null, global);
goog.exportSymbol('proto.kwaipilot.ChatModel', null, global);
goog.exportSymbol('proto.kwaipilot.ChatModelListQuery', null, global);
goog.exportSymbol('proto.kwaipilot.ChatModelListResponse', null, global);
goog.exportSymbol('proto.kwaipilot.CodeSearchData', null, global);
goog.exportSymbol('proto.kwaipilot.CodeSearchRequest', null, global);
goog.exportSymbol('proto.kwaipilot.CodeSearchResponse', null, global);
goog.exportSymbol('proto.kwaipilot.Knowledge', null, global);
goog.exportSymbol('proto.kwaipilot.KnowledgeRepoSearchRequest', null, global);
goog.exportSymbol('proto.kwaipilot.KnowledgeRepoSearchResponse', null, global);
goog.exportSymbol('proto.kwaipilot.ToolAuthConfig', null, global);
goog.exportSymbol('proto.kwaipilot.ToolDetailRequest', null, global);
goog.exportSymbol('proto.kwaipilot.ToolDetailResponse', null, global);
goog.exportSymbol('proto.kwaipilot.ToolParamsSchema', null, global);
goog.exportSymbol('proto.kwaipilot.UserInfo', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.ChatModelListQuery = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.ChatModelListQuery, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.ChatModelListQuery.displayName = 'proto.kwaipilot.ChatModelListQuery';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.ChatModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.ChatModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.ChatModel.displayName = 'proto.kwaipilot.ChatModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.UserInfo = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.UserInfo, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.UserInfo.displayName = 'proto.kwaipilot.UserInfo';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.ChatModelListResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.ChatModelListResponse.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.ChatModelListResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.ChatModelListResponse.displayName = 'proto.kwaipilot.ChatModelListResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.AgentModelListQuery = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.AgentModelListQuery, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.AgentModelListQuery.displayName = 'proto.kwaipilot.AgentModelListQuery';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.AgentModel = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.AgentModel.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.AgentModel, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.AgentModel.displayName = 'proto.kwaipilot.AgentModel';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.AgentModelListResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.AgentModelListResponse.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.AgentModelListResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.AgentModelListResponse.displayName = 'proto.kwaipilot.AgentModelListResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.KnowledgeRepoSearchRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.KnowledgeRepoSearchRequest.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.KnowledgeRepoSearchRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.KnowledgeRepoSearchRequest.displayName = 'proto.kwaipilot.KnowledgeRepoSearchRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.ChatMessage = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.ChatMessage, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.ChatMessage.displayName = 'proto.kwaipilot.ChatMessage';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.Knowledge = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.Knowledge, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.Knowledge.displayName = 'proto.kwaipilot.Knowledge';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.KnowledgeRepoSearchResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.KnowledgeRepoSearchResponse.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.KnowledgeRepoSearchResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.KnowledgeRepoSearchResponse.displayName = 'proto.kwaipilot.KnowledgeRepoSearchResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.ToolDetailRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.ToolDetailRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.ToolDetailRequest.displayName = 'proto.kwaipilot.ToolDetailRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.ToolParamsSchema = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.ToolParamsSchema, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.ToolParamsSchema.displayName = 'proto.kwaipilot.ToolParamsSchema';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.ToolAuthConfig = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.ToolAuthConfig, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.ToolAuthConfig.displayName = 'proto.kwaipilot.ToolAuthConfig';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.ToolDetailResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.ToolDetailResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.ToolDetailResponse.displayName = 'proto.kwaipilot.ToolDetailResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.ChatFile = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.ChatFile, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.ChatFile.displayName = 'proto.kwaipilot.ChatFile';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.CodeSearchData = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.CodeSearchData, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.CodeSearchData.displayName = 'proto.kwaipilot.CodeSearchData';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.CodeSearchRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.CodeSearchRequest.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.CodeSearchRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.CodeSearchRequest.displayName = 'proto.kwaipilot.CodeSearchRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.CodeSearchResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.CodeSearchResponse.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.CodeSearchResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.CodeSearchResponse.displayName = 'proto.kwaipilot.CodeSearchResponse';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.ChatModelListQuery.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.ChatModelListQuery.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.ChatModelListQuery} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ChatModelListQuery.toObject = function(includeInstance, msg) {
  var f, obj = {
    username: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.ChatModelListQuery}
 */
proto.kwaipilot.ChatModelListQuery.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.ChatModelListQuery;
  return proto.kwaipilot.ChatModelListQuery.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.ChatModelListQuery} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.ChatModelListQuery}
 */
proto.kwaipilot.ChatModelListQuery.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setUsername(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.ChatModelListQuery.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.ChatModelListQuery.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.ChatModelListQuery} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ChatModelListQuery.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getUsername();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string username = 1;
 * @return {string}
 */
proto.kwaipilot.ChatModelListQuery.prototype.getUsername = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ChatModelListQuery} returns this
 */
proto.kwaipilot.ChatModelListQuery.prototype.setUsername = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.ChatModel.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.ChatModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.ChatModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ChatModel.toObject = function(includeInstance, msg) {
  var f, obj = {
    code: jspb.Message.getFieldWithDefault(msg, 1, ""),
    name: jspb.Message.getFieldWithDefault(msg, 2, ""),
    desc: jspb.Message.getFieldWithDefault(msg, 3, ""),
    icon: jspb.Message.getFieldWithDefault(msg, 4, ""),
    disable: jspb.Message.getBooleanFieldWithDefault(msg, 5, false),
    maxInputLength: jspb.Message.getFieldWithDefault(msg, 6, 0),
    disableIcon: jspb.Message.getFieldWithDefault(msg, 7, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.ChatModel}
 */
proto.kwaipilot.ChatModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.ChatModel;
  return proto.kwaipilot.ChatModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.ChatModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.ChatModel}
 */
proto.kwaipilot.ChatModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setDesc(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setIcon(value);
      break;
    case 5:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setDisable(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setMaxInputLength(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setDisableIcon(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.ChatModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.ChatModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.ChatModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ChatModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCode();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getDesc();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getIcon();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getDisable();
  if (f) {
    writer.writeBool(
      5,
      f
    );
  }
  f = message.getMaxInputLength();
  if (f !== 0) {
    writer.writeInt32(
      6,
      f
    );
  }
  f = message.getDisableIcon();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
};


/**
 * optional string code = 1;
 * @return {string}
 */
proto.kwaipilot.ChatModel.prototype.getCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ChatModel} returns this
 */
proto.kwaipilot.ChatModel.prototype.setCode = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string name = 2;
 * @return {string}
 */
proto.kwaipilot.ChatModel.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ChatModel} returns this
 */
proto.kwaipilot.ChatModel.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string desc = 3;
 * @return {string}
 */
proto.kwaipilot.ChatModel.prototype.getDesc = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ChatModel} returns this
 */
proto.kwaipilot.ChatModel.prototype.setDesc = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string icon = 4;
 * @return {string}
 */
proto.kwaipilot.ChatModel.prototype.getIcon = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ChatModel} returns this
 */
proto.kwaipilot.ChatModel.prototype.setIcon = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional bool disable = 5;
 * @return {boolean}
 */
proto.kwaipilot.ChatModel.prototype.getDisable = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 5, false));
};


/**
 * @param {boolean} value
 * @return {!proto.kwaipilot.ChatModel} returns this
 */
proto.kwaipilot.ChatModel.prototype.setDisable = function(value) {
  return jspb.Message.setProto3BooleanField(this, 5, value);
};


/**
 * optional int32 max_input_length = 6;
 * @return {number}
 */
proto.kwaipilot.ChatModel.prototype.getMaxInputLength = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.ChatModel} returns this
 */
proto.kwaipilot.ChatModel.prototype.setMaxInputLength = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional string disable_icon = 7;
 * @return {string}
 */
proto.kwaipilot.ChatModel.prototype.getDisableIcon = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ChatModel} returns this
 */
proto.kwaipilot.ChatModel.prototype.setDisableIcon = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.UserInfo.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.UserInfo.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.UserInfo} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.UserInfo.toObject = function(includeInstance, msg) {
  var f, obj = {
    username: jspb.Message.getFieldWithDefault(msg, 1, ""),
    avatarUrl: jspb.Message.getFieldWithDefault(msg, 2, ""),
    name: jspb.Message.getFieldWithDefault(msg, 3, ""),
    dept1: jspb.Message.getFieldWithDefault(msg, 4, ""),
    deptCode: jspb.Message.getFieldWithDefault(msg, 5, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.UserInfo}
 */
proto.kwaipilot.UserInfo.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.UserInfo;
  return proto.kwaipilot.UserInfo.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.UserInfo} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.UserInfo}
 */
proto.kwaipilot.UserInfo.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setUsername(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setAvatarUrl(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setDept1(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setDeptCode(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.UserInfo.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.UserInfo.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.UserInfo} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.UserInfo.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getUsername();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getAvatarUrl();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getDept1();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getDeptCode();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
};


/**
 * optional string username = 1;
 * @return {string}
 */
proto.kwaipilot.UserInfo.prototype.getUsername = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.UserInfo} returns this
 */
proto.kwaipilot.UserInfo.prototype.setUsername = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string avatar_url = 2;
 * @return {string}
 */
proto.kwaipilot.UserInfo.prototype.getAvatarUrl = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.UserInfo} returns this
 */
proto.kwaipilot.UserInfo.prototype.setAvatarUrl = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string name = 3;
 * @return {string}
 */
proto.kwaipilot.UserInfo.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.UserInfo} returns this
 */
proto.kwaipilot.UserInfo.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string dept1 = 4;
 * @return {string}
 */
proto.kwaipilot.UserInfo.prototype.getDept1 = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.UserInfo} returns this
 */
proto.kwaipilot.UserInfo.prototype.setDept1 = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string dept_code = 5;
 * @return {string}
 */
proto.kwaipilot.UserInfo.prototype.getDeptCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.UserInfo} returns this
 */
proto.kwaipilot.UserInfo.prototype.setDeptCode = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.ChatModelListResponse.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.ChatModelListResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.ChatModelListResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.ChatModelListResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ChatModelListResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    chatModelsList: jspb.Message.toObjectList(msg.getChatModelsList(),
    proto.kwaipilot.ChatModel.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.ChatModelListResponse}
 */
proto.kwaipilot.ChatModelListResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.ChatModelListResponse;
  return proto.kwaipilot.ChatModelListResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.ChatModelListResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.ChatModelListResponse}
 */
proto.kwaipilot.ChatModelListResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.kwaipilot.ChatModel;
      reader.readMessage(value,proto.kwaipilot.ChatModel.deserializeBinaryFromReader);
      msg.addChatModels(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.ChatModelListResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.ChatModelListResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.ChatModelListResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ChatModelListResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getChatModelsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.kwaipilot.ChatModel.serializeBinaryToWriter
    );
  }
};


/**
 * repeated ChatModel chat_models = 1;
 * @return {!Array<!proto.kwaipilot.ChatModel>}
 */
proto.kwaipilot.ChatModelListResponse.prototype.getChatModelsList = function() {
  return /** @type{!Array<!proto.kwaipilot.ChatModel>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.ChatModel, 1));
};


/**
 * @param {!Array<!proto.kwaipilot.ChatModel>} value
 * @return {!proto.kwaipilot.ChatModelListResponse} returns this
*/
proto.kwaipilot.ChatModelListResponse.prototype.setChatModelsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.kwaipilot.ChatModel=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.ChatModel}
 */
proto.kwaipilot.ChatModelListResponse.prototype.addChatModels = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.kwaipilot.ChatModel, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.ChatModelListResponse} returns this
 */
proto.kwaipilot.ChatModelListResponse.prototype.clearChatModelsList = function() {
  return this.setChatModelsList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.AgentModelListQuery.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.AgentModelListQuery.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.AgentModelListQuery} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.AgentModelListQuery.toObject = function(includeInstance, msg) {
  var f, obj = {
    limit: jspb.Message.getFieldWithDefault(msg, 1, 0),
    offset: jspb.Message.getFieldWithDefault(msg, 2, 0),
    name: jspb.Message.getFieldWithDefault(msg, 3, ""),
    orderType: jspb.Message.getFieldWithDefault(msg, 4, 0),
    username: jspb.Message.getFieldWithDefault(msg, 5, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.AgentModelListQuery}
 */
proto.kwaipilot.AgentModelListQuery.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.AgentModelListQuery;
  return proto.kwaipilot.AgentModelListQuery.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.AgentModelListQuery} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.AgentModelListQuery}
 */
proto.kwaipilot.AgentModelListQuery.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setLimit(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setOffset(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setOrderType(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setUsername(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.AgentModelListQuery.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.AgentModelListQuery.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.AgentModelListQuery} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.AgentModelListQuery.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLimit();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getOffset();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getOrderType();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getUsername();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
};


/**
 * optional int32 limit = 1;
 * @return {number}
 */
proto.kwaipilot.AgentModelListQuery.prototype.getLimit = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.AgentModelListQuery} returns this
 */
proto.kwaipilot.AgentModelListQuery.prototype.setLimit = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional int32 offset = 2;
 * @return {number}
 */
proto.kwaipilot.AgentModelListQuery.prototype.getOffset = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.AgentModelListQuery} returns this
 */
proto.kwaipilot.AgentModelListQuery.prototype.setOffset = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional string name = 3;
 * @return {string}
 */
proto.kwaipilot.AgentModelListQuery.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.AgentModelListQuery} returns this
 */
proto.kwaipilot.AgentModelListQuery.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional int32 order_type = 4;
 * @return {number}
 */
proto.kwaipilot.AgentModelListQuery.prototype.getOrderType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.AgentModelListQuery} returns this
 */
proto.kwaipilot.AgentModelListQuery.prototype.setOrderType = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional string username = 5;
 * @return {string}
 */
proto.kwaipilot.AgentModelListQuery.prototype.getUsername = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.AgentModelListQuery} returns this
 */
proto.kwaipilot.AgentModelListQuery.prototype.setUsername = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.AgentModel.repeatedFields_ = [7];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.AgentModel.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.AgentModel.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.AgentModel} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.AgentModel.toObject = function(includeInstance, msg) {
  var f, obj = {
    auid: jspb.Message.getFieldWithDefault(msg, 1, ""),
    createTime: jspb.Message.getFieldWithDefault(msg, 2, 0),
    description: jspb.Message.getFieldWithDefault(msg, 3, ""),
    icon: jspb.Message.getFieldWithDefault(msg, 4, ""),
    id: jspb.Message.getFieldWithDefault(msg, 5, 0),
    locked: jspb.Message.getBooleanFieldWithDefault(msg, 6, false),
    maintainerListList: jspb.Message.toObjectList(msg.getMaintainerListList(),
    proto.kwaipilot.UserInfo.toObject, includeInstance),
    name: jspb.Message.getFieldWithDefault(msg, 8, ""),
    official: jspb.Message.getBooleanFieldWithDefault(msg, 9, false),
    parentAgentId: jspb.Message.getFieldWithDefault(msg, 10, 0),
    refCount: jspb.Message.getFieldWithDefault(msg, 11, 0),
    roleDescription: jspb.Message.getFieldWithDefault(msg, 12, ""),
    status: jspb.Message.getFieldWithDefault(msg, 13, ""),
    updateTime: jspb.Message.getFieldWithDefault(msg, 14, 0),
    updater: (f = msg.getUpdater()) && proto.kwaipilot.UserInfo.toObject(includeInstance, f),
    visibility: jspb.Message.getFieldWithDefault(msg, 16, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.AgentModel}
 */
proto.kwaipilot.AgentModel.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.AgentModel;
  return proto.kwaipilot.AgentModel.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.AgentModel} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.AgentModel}
 */
proto.kwaipilot.AgentModel.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setAuid(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setCreateTime(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setDescription(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setIcon(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setId(value);
      break;
    case 6:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setLocked(value);
      break;
    case 7:
      var value = new proto.kwaipilot.UserInfo;
      reader.readMessage(value,proto.kwaipilot.UserInfo.deserializeBinaryFromReader);
      msg.addMaintainerList(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 9:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setOfficial(value);
      break;
    case 10:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setParentAgentId(value);
      break;
    case 11:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setRefCount(value);
      break;
    case 12:
      var value = /** @type {string} */ (reader.readString());
      msg.setRoleDescription(value);
      break;
    case 13:
      var value = /** @type {string} */ (reader.readString());
      msg.setStatus(value);
      break;
    case 14:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setUpdateTime(value);
      break;
    case 15:
      var value = new proto.kwaipilot.UserInfo;
      reader.readMessage(value,proto.kwaipilot.UserInfo.deserializeBinaryFromReader);
      msg.setUpdater(value);
      break;
    case 16:
      var value = /** @type {string} */ (reader.readString());
      msg.setVisibility(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.AgentModel.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.AgentModel.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.AgentModel} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.AgentModel.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAuid();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getCreateTime();
  if (f !== 0) {
    writer.writeInt64(
      2,
      f
    );
  }
  f = message.getDescription();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getIcon();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getId();
  if (f !== 0) {
    writer.writeInt64(
      5,
      f
    );
  }
  f = message.getLocked();
  if (f) {
    writer.writeBool(
      6,
      f
    );
  }
  f = message.getMaintainerListList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      7,
      f,
      proto.kwaipilot.UserInfo.serializeBinaryToWriter
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getOfficial();
  if (f) {
    writer.writeBool(
      9,
      f
    );
  }
  f = message.getParentAgentId();
  if (f !== 0) {
    writer.writeInt64(
      10,
      f
    );
  }
  f = message.getRefCount();
  if (f !== 0) {
    writer.writeInt64(
      11,
      f
    );
  }
  f = message.getRoleDescription();
  if (f.length > 0) {
    writer.writeString(
      12,
      f
    );
  }
  f = message.getStatus();
  if (f.length > 0) {
    writer.writeString(
      13,
      f
    );
  }
  f = message.getUpdateTime();
  if (f !== 0) {
    writer.writeInt64(
      14,
      f
    );
  }
  f = message.getUpdater();
  if (f != null) {
    writer.writeMessage(
      15,
      f,
      proto.kwaipilot.UserInfo.serializeBinaryToWriter
    );
  }
  f = message.getVisibility();
  if (f.length > 0) {
    writer.writeString(
      16,
      f
    );
  }
};


/**
 * optional string auid = 1;
 * @return {string}
 */
proto.kwaipilot.AgentModel.prototype.getAuid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.setAuid = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional int64 create_time = 2;
 * @return {number}
 */
proto.kwaipilot.AgentModel.prototype.getCreateTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.setCreateTime = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional string description = 3;
 * @return {string}
 */
proto.kwaipilot.AgentModel.prototype.getDescription = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.setDescription = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string icon = 4;
 * @return {string}
 */
proto.kwaipilot.AgentModel.prototype.getIcon = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.setIcon = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional int64 id = 5;
 * @return {number}
 */
proto.kwaipilot.AgentModel.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.setId = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional bool locked = 6;
 * @return {boolean}
 */
proto.kwaipilot.AgentModel.prototype.getLocked = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 6, false));
};


/**
 * @param {boolean} value
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.setLocked = function(value) {
  return jspb.Message.setProto3BooleanField(this, 6, value);
};


/**
 * repeated UserInfo maintainer_list = 7;
 * @return {!Array<!proto.kwaipilot.UserInfo>}
 */
proto.kwaipilot.AgentModel.prototype.getMaintainerListList = function() {
  return /** @type{!Array<!proto.kwaipilot.UserInfo>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.UserInfo, 7));
};


/**
 * @param {!Array<!proto.kwaipilot.UserInfo>} value
 * @return {!proto.kwaipilot.AgentModel} returns this
*/
proto.kwaipilot.AgentModel.prototype.setMaintainerListList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 7, value);
};


/**
 * @param {!proto.kwaipilot.UserInfo=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.UserInfo}
 */
proto.kwaipilot.AgentModel.prototype.addMaintainerList = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 7, opt_value, proto.kwaipilot.UserInfo, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.clearMaintainerListList = function() {
  return this.setMaintainerListList([]);
};


/**
 * optional string name = 8;
 * @return {string}
 */
proto.kwaipilot.AgentModel.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional bool official = 9;
 * @return {boolean}
 */
proto.kwaipilot.AgentModel.prototype.getOfficial = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 9, false));
};


/**
 * @param {boolean} value
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.setOfficial = function(value) {
  return jspb.Message.setProto3BooleanField(this, 9, value);
};


/**
 * optional int64 parent_agent_id = 10;
 * @return {number}
 */
proto.kwaipilot.AgentModel.prototype.getParentAgentId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 10, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.setParentAgentId = function(value) {
  return jspb.Message.setProto3IntField(this, 10, value);
};


/**
 * optional int64 ref_count = 11;
 * @return {number}
 */
proto.kwaipilot.AgentModel.prototype.getRefCount = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 11, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.setRefCount = function(value) {
  return jspb.Message.setProto3IntField(this, 11, value);
};


/**
 * optional string role_description = 12;
 * @return {string}
 */
proto.kwaipilot.AgentModel.prototype.getRoleDescription = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 12, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.setRoleDescription = function(value) {
  return jspb.Message.setProto3StringField(this, 12, value);
};


/**
 * optional string status = 13;
 * @return {string}
 */
proto.kwaipilot.AgentModel.prototype.getStatus = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 13, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.setStatus = function(value) {
  return jspb.Message.setProto3StringField(this, 13, value);
};


/**
 * optional int64 update_time = 14;
 * @return {number}
 */
proto.kwaipilot.AgentModel.prototype.getUpdateTime = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 14, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.setUpdateTime = function(value) {
  return jspb.Message.setProto3IntField(this, 14, value);
};


/**
 * optional UserInfo updater = 15;
 * @return {?proto.kwaipilot.UserInfo}
 */
proto.kwaipilot.AgentModel.prototype.getUpdater = function() {
  return /** @type{?proto.kwaipilot.UserInfo} */ (
    jspb.Message.getWrapperField(this, proto.kwaipilot.UserInfo, 15));
};


/**
 * @param {?proto.kwaipilot.UserInfo|undefined} value
 * @return {!proto.kwaipilot.AgentModel} returns this
*/
proto.kwaipilot.AgentModel.prototype.setUpdater = function(value) {
  return jspb.Message.setWrapperField(this, 15, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.clearUpdater = function() {
  return this.setUpdater(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.kwaipilot.AgentModel.prototype.hasUpdater = function() {
  return jspb.Message.getField(this, 15) != null;
};


/**
 * optional string visibility = 16;
 * @return {string}
 */
proto.kwaipilot.AgentModel.prototype.getVisibility = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 16, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.AgentModel} returns this
 */
proto.kwaipilot.AgentModel.prototype.setVisibility = function(value) {
  return jspb.Message.setProto3StringField(this, 16, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.AgentModelListResponse.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.AgentModelListResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.AgentModelListResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.AgentModelListResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.AgentModelListResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    listList: jspb.Message.toObjectList(msg.getListList(),
    proto.kwaipilot.AgentModel.toObject, includeInstance),
    total: jspb.Message.getFieldWithDefault(msg, 2, 0),
    pageNum: jspb.Message.getFieldWithDefault(msg, 3, 0),
    pageSize: jspb.Message.getFieldWithDefault(msg, 4, 0),
    pages: jspb.Message.getFieldWithDefault(msg, 5, 0),
    hasMore: jspb.Message.getBooleanFieldWithDefault(msg, 6, false)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.AgentModelListResponse}
 */
proto.kwaipilot.AgentModelListResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.AgentModelListResponse;
  return proto.kwaipilot.AgentModelListResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.AgentModelListResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.AgentModelListResponse}
 */
proto.kwaipilot.AgentModelListResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.kwaipilot.AgentModel;
      reader.readMessage(value,proto.kwaipilot.AgentModel.deserializeBinaryFromReader);
      msg.addList(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setTotal(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPageNum(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPageSize(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPages(value);
      break;
    case 6:
      var value = /** @type {boolean} */ (reader.readBool());
      msg.setHasMore(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.AgentModelListResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.AgentModelListResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.AgentModelListResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.AgentModelListResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getListList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.kwaipilot.AgentModel.serializeBinaryToWriter
    );
  }
  f = message.getTotal();
  if (f !== 0) {
    writer.writeInt64(
      2,
      f
    );
  }
  f = message.getPageNum();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getPageSize();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getPages();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getHasMore();
  if (f) {
    writer.writeBool(
      6,
      f
    );
  }
};


/**
 * repeated AgentModel list = 1;
 * @return {!Array<!proto.kwaipilot.AgentModel>}
 */
proto.kwaipilot.AgentModelListResponse.prototype.getListList = function() {
  return /** @type{!Array<!proto.kwaipilot.AgentModel>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.AgentModel, 1));
};


/**
 * @param {!Array<!proto.kwaipilot.AgentModel>} value
 * @return {!proto.kwaipilot.AgentModelListResponse} returns this
*/
proto.kwaipilot.AgentModelListResponse.prototype.setListList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.kwaipilot.AgentModel=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.AgentModel}
 */
proto.kwaipilot.AgentModelListResponse.prototype.addList = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.kwaipilot.AgentModel, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.AgentModelListResponse} returns this
 */
proto.kwaipilot.AgentModelListResponse.prototype.clearListList = function() {
  return this.setListList([]);
};


/**
 * optional int64 total = 2;
 * @return {number}
 */
proto.kwaipilot.AgentModelListResponse.prototype.getTotal = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.AgentModelListResponse} returns this
 */
proto.kwaipilot.AgentModelListResponse.prototype.setTotal = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional int32 page_num = 3;
 * @return {number}
 */
proto.kwaipilot.AgentModelListResponse.prototype.getPageNum = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.AgentModelListResponse} returns this
 */
proto.kwaipilot.AgentModelListResponse.prototype.setPageNum = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int32 page_size = 4;
 * @return {number}
 */
proto.kwaipilot.AgentModelListResponse.prototype.getPageSize = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.AgentModelListResponse} returns this
 */
proto.kwaipilot.AgentModelListResponse.prototype.setPageSize = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int32 pages = 5;
 * @return {number}
 */
proto.kwaipilot.AgentModelListResponse.prototype.getPages = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.AgentModelListResponse} returns this
 */
proto.kwaipilot.AgentModelListResponse.prototype.setPages = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional bool has_more = 6;
 * @return {boolean}
 */
proto.kwaipilot.AgentModelListResponse.prototype.getHasMore = function() {
  return /** @type {boolean} */ (jspb.Message.getBooleanFieldWithDefault(this, 6, false));
};


/**
 * @param {boolean} value
 * @return {!proto.kwaipilot.AgentModelListResponse} returns this
 */
proto.kwaipilot.AgentModelListResponse.prototype.setHasMore = function(value) {
  return jspb.Message.setProto3BooleanField(this, 6, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.repeatedFields_ = [2,5];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.KnowledgeRepoSearchRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.KnowledgeRepoSearchRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    platform: jspb.Message.getFieldWithDefault(msg, 1, ""),
    knowledgeRepoIdsList: (f = jspb.Message.getRepeatedField(msg, 2)) == null ? undefined : f,
    query: jspb.Message.getFieldWithDefault(msg, 3, ""),
    topK: jspb.Message.getFieldWithDefault(msg, 4, 0),
    chatHistoryList: jspb.Message.toObjectList(msg.getChatHistoryList(),
    proto.kwaipilot.ChatMessage.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.KnowledgeRepoSearchRequest}
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.KnowledgeRepoSearchRequest;
  return proto.kwaipilot.KnowledgeRepoSearchRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.KnowledgeRepoSearchRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.KnowledgeRepoSearchRequest}
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPlatform(value);
      break;
    case 2:
      var values = /** @type {!Array<number>} */ (reader.isDelimited() ? reader.readPackedInt64() : [reader.readInt64()]);
      for (var i = 0; i < values.length; i++) {
        msg.addKnowledgeRepoIds(values[i]);
      }
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setQuery(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setTopK(value);
      break;
    case 5:
      var value = new proto.kwaipilot.ChatMessage;
      reader.readMessage(value,proto.kwaipilot.ChatMessage.deserializeBinaryFromReader);
      msg.addChatHistory(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.KnowledgeRepoSearchRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.KnowledgeRepoSearchRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPlatform();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getKnowledgeRepoIdsList();
  if (f.length > 0) {
    writer.writePackedInt64(
      2,
      f
    );
  }
  f = message.getQuery();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getTopK();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getChatHistoryList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      5,
      f,
      proto.kwaipilot.ChatMessage.serializeBinaryToWriter
    );
  }
};


/**
 * optional string platform = 1;
 * @return {string}
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.getPlatform = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.setPlatform = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * repeated int64 knowledge_repo_ids = 2;
 * @return {!Array<number>}
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.getKnowledgeRepoIdsList = function() {
  return /** @type {!Array<number>} */ (jspb.Message.getRepeatedField(this, 2));
};


/**
 * @param {!Array<number>} value
 * @return {!proto.kwaipilot.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.setKnowledgeRepoIdsList = function(value) {
  return jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {number} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.addKnowledgeRepoIds = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.clearKnowledgeRepoIdsList = function() {
  return this.setKnowledgeRepoIdsList([]);
};


/**
 * optional string query = 3;
 * @return {string}
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.getQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.setQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional int32 top_k = 4;
 * @return {number}
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.getTopK = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.setTopK = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * repeated ChatMessage chat_history = 5;
 * @return {!Array<!proto.kwaipilot.ChatMessage>}
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.getChatHistoryList = function() {
  return /** @type{!Array<!proto.kwaipilot.ChatMessage>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.ChatMessage, 5));
};


/**
 * @param {!Array<!proto.kwaipilot.ChatMessage>} value
 * @return {!proto.kwaipilot.KnowledgeRepoSearchRequest} returns this
*/
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.setChatHistoryList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 5, value);
};


/**
 * @param {!proto.kwaipilot.ChatMessage=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.ChatMessage}
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.addChatHistory = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 5, opt_value, proto.kwaipilot.ChatMessage, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot.KnowledgeRepoSearchRequest.prototype.clearChatHistoryList = function() {
  return this.setChatHistoryList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.ChatMessage.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.ChatMessage.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.ChatMessage} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ChatMessage.toObject = function(includeInstance, msg) {
  var f, obj = {
    role: jspb.Message.getFieldWithDefault(msg, 1, ""),
    content: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.ChatMessage}
 */
proto.kwaipilot.ChatMessage.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.ChatMessage;
  return proto.kwaipilot.ChatMessage.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.ChatMessage} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.ChatMessage}
 */
proto.kwaipilot.ChatMessage.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setRole(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.ChatMessage.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.ChatMessage.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.ChatMessage} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ChatMessage.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRole();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getContent();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string role = 1;
 * @return {string}
 */
proto.kwaipilot.ChatMessage.prototype.getRole = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ChatMessage} returns this
 */
proto.kwaipilot.ChatMessage.prototype.setRole = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string content = 2;
 * @return {string}
 */
proto.kwaipilot.ChatMessage.prototype.getContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ChatMessage} returns this
 */
proto.kwaipilot.ChatMessage.prototype.setContent = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.Knowledge.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.Knowledge.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.Knowledge} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.Knowledge.toObject = function(includeInstance, msg) {
  var f, obj = {
    content: jspb.Message.getFieldWithDefault(msg, 1, ""),
    link: jspb.Message.getFieldWithDefault(msg, 2, ""),
    title: jspb.Message.getFieldWithDefault(msg, 3, ""),
    knowledgeRepoId: jspb.Message.getFieldWithDefault(msg, 4, ""),
    knowledgeRepoName: jspb.Message.getFieldWithDefault(msg, 5, ""),
    score: jspb.Message.getFloatingPointFieldWithDefault(msg, 6, 0.0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.Knowledge}
 */
proto.kwaipilot.Knowledge.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.Knowledge;
  return proto.kwaipilot.Knowledge.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.Knowledge} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.Knowledge}
 */
proto.kwaipilot.Knowledge.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setContent(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setLink(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setTitle(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setKnowledgeRepoId(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setKnowledgeRepoName(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setScore(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.Knowledge.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.Knowledge.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.Knowledge} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.Knowledge.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getContent();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getLink();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTitle();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getKnowledgeRepoId();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getKnowledgeRepoName();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getScore();
  if (f !== 0.0) {
    writer.writeFloat(
      6,
      f
    );
  }
};


/**
 * optional string content = 1;
 * @return {string}
 */
proto.kwaipilot.Knowledge.prototype.getContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.Knowledge} returns this
 */
proto.kwaipilot.Knowledge.prototype.setContent = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string link = 2;
 * @return {string}
 */
proto.kwaipilot.Knowledge.prototype.getLink = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.Knowledge} returns this
 */
proto.kwaipilot.Knowledge.prototype.setLink = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string title = 3;
 * @return {string}
 */
proto.kwaipilot.Knowledge.prototype.getTitle = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.Knowledge} returns this
 */
proto.kwaipilot.Knowledge.prototype.setTitle = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string knowledge_repo_id = 4;
 * @return {string}
 */
proto.kwaipilot.Knowledge.prototype.getKnowledgeRepoId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.Knowledge} returns this
 */
proto.kwaipilot.Knowledge.prototype.setKnowledgeRepoId = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string knowledge_repo_name = 5;
 * @return {string}
 */
proto.kwaipilot.Knowledge.prototype.getKnowledgeRepoName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.Knowledge} returns this
 */
proto.kwaipilot.Knowledge.prototype.setKnowledgeRepoName = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional float score = 6;
 * @return {number}
 */
proto.kwaipilot.Knowledge.prototype.getScore = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 6, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.Knowledge} returns this
 */
proto.kwaipilot.Knowledge.prototype.setScore = function(value) {
  return jspb.Message.setProto3FloatField(this, 6, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.KnowledgeRepoSearchResponse.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.KnowledgeRepoSearchResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.KnowledgeRepoSearchResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.KnowledgeRepoSearchResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.KnowledgeRepoSearchResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    knowledgesList: jspb.Message.toObjectList(msg.getKnowledgesList(),
    proto.kwaipilot.Knowledge.toObject, includeInstance),
    prompt: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.KnowledgeRepoSearchResponse}
 */
proto.kwaipilot.KnowledgeRepoSearchResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.KnowledgeRepoSearchResponse;
  return proto.kwaipilot.KnowledgeRepoSearchResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.KnowledgeRepoSearchResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.KnowledgeRepoSearchResponse}
 */
proto.kwaipilot.KnowledgeRepoSearchResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.kwaipilot.Knowledge;
      reader.readMessage(value,proto.kwaipilot.Knowledge.deserializeBinaryFromReader);
      msg.addKnowledges(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setPrompt(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.KnowledgeRepoSearchResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.KnowledgeRepoSearchResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.KnowledgeRepoSearchResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.KnowledgeRepoSearchResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getKnowledgesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.kwaipilot.Knowledge.serializeBinaryToWriter
    );
  }
  f = message.getPrompt();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * repeated Knowledge knowledges = 1;
 * @return {!Array<!proto.kwaipilot.Knowledge>}
 */
proto.kwaipilot.KnowledgeRepoSearchResponse.prototype.getKnowledgesList = function() {
  return /** @type{!Array<!proto.kwaipilot.Knowledge>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.Knowledge, 1));
};


/**
 * @param {!Array<!proto.kwaipilot.Knowledge>} value
 * @return {!proto.kwaipilot.KnowledgeRepoSearchResponse} returns this
*/
proto.kwaipilot.KnowledgeRepoSearchResponse.prototype.setKnowledgesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.kwaipilot.Knowledge=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.Knowledge}
 */
proto.kwaipilot.KnowledgeRepoSearchResponse.prototype.addKnowledges = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.kwaipilot.Knowledge, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.KnowledgeRepoSearchResponse} returns this
 */
proto.kwaipilot.KnowledgeRepoSearchResponse.prototype.clearKnowledgesList = function() {
  return this.setKnowledgesList([]);
};


/**
 * optional string prompt = 2;
 * @return {string}
 */
proto.kwaipilot.KnowledgeRepoSearchResponse.prototype.getPrompt = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.KnowledgeRepoSearchResponse} returns this
 */
proto.kwaipilot.KnowledgeRepoSearchResponse.prototype.setPrompt = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.ToolDetailRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.ToolDetailRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.ToolDetailRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ToolDetailRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    toolId: jspb.Message.getFieldWithDefault(msg, 1, 0),
    tuid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    username: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.ToolDetailRequest}
 */
proto.kwaipilot.ToolDetailRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.ToolDetailRequest;
  return proto.kwaipilot.ToolDetailRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.ToolDetailRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.ToolDetailRequest}
 */
proto.kwaipilot.ToolDetailRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setToolId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTuid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setUsername(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.ToolDetailRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.ToolDetailRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.ToolDetailRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ToolDetailRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getToolId();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getTuid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getUsername();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional int64 tool_id = 1;
 * @return {number}
 */
proto.kwaipilot.ToolDetailRequest.prototype.getToolId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.ToolDetailRequest} returns this
 */
proto.kwaipilot.ToolDetailRequest.prototype.setToolId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string tuid = 2;
 * @return {string}
 */
proto.kwaipilot.ToolDetailRequest.prototype.getTuid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolDetailRequest} returns this
 */
proto.kwaipilot.ToolDetailRequest.prototype.setTuid = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string username = 3;
 * @return {string}
 */
proto.kwaipilot.ToolDetailRequest.prototype.getUsername = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolDetailRequest} returns this
 */
proto.kwaipilot.ToolDetailRequest.prototype.setUsername = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.ToolParamsSchema.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.ToolParamsSchema.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.ToolParamsSchema} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ToolParamsSchema.toObject = function(includeInstance, msg) {
  var f, obj = {
    bodyParamsSchema: jspb.Message.getFieldWithDefault(msg, 1, ""),
    queryParamsSchema: jspb.Message.getFieldWithDefault(msg, 2, ""),
    headerParamsSchema: jspb.Message.getFieldWithDefault(msg, 3, ""),
    pathParamsSchema: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.ToolParamsSchema}
 */
proto.kwaipilot.ToolParamsSchema.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.ToolParamsSchema;
  return proto.kwaipilot.ToolParamsSchema.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.ToolParamsSchema} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.ToolParamsSchema}
 */
proto.kwaipilot.ToolParamsSchema.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setBodyParamsSchema(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setQueryParamsSchema(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setHeaderParamsSchema(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setPathParamsSchema(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.ToolParamsSchema.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.ToolParamsSchema.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.ToolParamsSchema} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ToolParamsSchema.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getBodyParamsSchema();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getQueryParamsSchema();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getHeaderParamsSchema();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getPathParamsSchema();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional string body_params_schema = 1;
 * @return {string}
 */
proto.kwaipilot.ToolParamsSchema.prototype.getBodyParamsSchema = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolParamsSchema} returns this
 */
proto.kwaipilot.ToolParamsSchema.prototype.setBodyParamsSchema = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string query_params_schema = 2;
 * @return {string}
 */
proto.kwaipilot.ToolParamsSchema.prototype.getQueryParamsSchema = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolParamsSchema} returns this
 */
proto.kwaipilot.ToolParamsSchema.prototype.setQueryParamsSchema = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string header_params_schema = 3;
 * @return {string}
 */
proto.kwaipilot.ToolParamsSchema.prototype.getHeaderParamsSchema = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolParamsSchema} returns this
 */
proto.kwaipilot.ToolParamsSchema.prototype.setHeaderParamsSchema = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string path_params_schema = 4;
 * @return {string}
 */
proto.kwaipilot.ToolParamsSchema.prototype.getPathParamsSchema = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolParamsSchema} returns this
 */
proto.kwaipilot.ToolParamsSchema.prototype.setPathParamsSchema = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.ToolAuthConfig.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.ToolAuthConfig.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.ToolAuthConfig} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ToolAuthConfig.toObject = function(includeInstance, msg) {
  var f, obj = {
    authType: jspb.Message.getFieldWithDefault(msg, 1, 0),
    location: jspb.Message.getFieldWithDefault(msg, 2, ""),
    name: jspb.Message.getFieldWithDefault(msg, 3, ""),
    value: jspb.Message.getFieldWithDefault(msg, 4, ""),
    appKey: jspb.Message.getFieldWithDefault(msg, 5, ""),
    secretKey: jspb.Message.getFieldWithDefault(msg, 6, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.ToolAuthConfig}
 */
proto.kwaipilot.ToolAuthConfig.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.ToolAuthConfig;
  return proto.kwaipilot.ToolAuthConfig.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.ToolAuthConfig} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.ToolAuthConfig}
 */
proto.kwaipilot.ToolAuthConfig.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setAuthType(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setLocation(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setValue(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setAppKey(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setSecretKey(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.ToolAuthConfig.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.ToolAuthConfig.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.ToolAuthConfig} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ToolAuthConfig.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getAuthType();
  if (f !== 0) {
    writer.writeInt32(
      1,
      f
    );
  }
  f = message.getLocation();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getValue();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getAppKey();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getSecretKey();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
};


/**
 * optional int32 auth_type = 1;
 * @return {number}
 */
proto.kwaipilot.ToolAuthConfig.prototype.getAuthType = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.ToolAuthConfig} returns this
 */
proto.kwaipilot.ToolAuthConfig.prototype.setAuthType = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string location = 2;
 * @return {string}
 */
proto.kwaipilot.ToolAuthConfig.prototype.getLocation = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolAuthConfig} returns this
 */
proto.kwaipilot.ToolAuthConfig.prototype.setLocation = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string name = 3;
 * @return {string}
 */
proto.kwaipilot.ToolAuthConfig.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolAuthConfig} returns this
 */
proto.kwaipilot.ToolAuthConfig.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string value = 4;
 * @return {string}
 */
proto.kwaipilot.ToolAuthConfig.prototype.getValue = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolAuthConfig} returns this
 */
proto.kwaipilot.ToolAuthConfig.prototype.setValue = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string app_key = 5;
 * @return {string}
 */
proto.kwaipilot.ToolAuthConfig.prototype.getAppKey = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolAuthConfig} returns this
 */
proto.kwaipilot.ToolAuthConfig.prototype.setAppKey = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string secret_key = 6;
 * @return {string}
 */
proto.kwaipilot.ToolAuthConfig.prototype.getSecretKey = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolAuthConfig} returns this
 */
proto.kwaipilot.ToolAuthConfig.prototype.setSecretKey = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.ToolDetailResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.ToolDetailResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.ToolDetailResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ToolDetailResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    tuid: jspb.Message.getFieldWithDefault(msg, 2, ""),
    name: jspb.Message.getFieldWithDefault(msg, 3, ""),
    icon: jspb.Message.getFieldWithDefault(msg, 4, ""),
    description: jspb.Message.getFieldWithDefault(msg, 5, ""),
    code: jspb.Message.getFieldWithDefault(msg, 6, ""),
    apiMethod: jspb.Message.getFieldWithDefault(msg, 7, ""),
    apiPath: jspb.Message.getFieldWithDefault(msg, 8, ""),
    enterParamsSchema: (f = msg.getEnterParamsSchema()) && proto.kwaipilot.ToolParamsSchema.toObject(includeInstance, f),
    outParamSchema: jspb.Message.getFieldWithDefault(msg, 10, ""),
    authConfig: (f = msg.getAuthConfig()) && proto.kwaipilot.ToolAuthConfig.toObject(includeInstance, f)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.ToolDetailResponse}
 */
proto.kwaipilot.ToolDetailResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.ToolDetailResponse;
  return proto.kwaipilot.ToolDetailResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.ToolDetailResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.ToolDetailResponse}
 */
proto.kwaipilot.ToolDetailResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setTuid(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setIcon(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setDescription(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setCode(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setApiMethod(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setApiPath(value);
      break;
    case 9:
      var value = new proto.kwaipilot.ToolParamsSchema;
      reader.readMessage(value,proto.kwaipilot.ToolParamsSchema.deserializeBinaryFromReader);
      msg.setEnterParamsSchema(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setOutParamSchema(value);
      break;
    case 11:
      var value = new proto.kwaipilot.ToolAuthConfig;
      reader.readMessage(value,proto.kwaipilot.ToolAuthConfig.deserializeBinaryFromReader);
      msg.setAuthConfig(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.ToolDetailResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.ToolDetailResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.ToolDetailResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ToolDetailResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getTuid();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getIcon();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getDescription();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getCode();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getApiMethod();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getApiPath();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getEnterParamsSchema();
  if (f != null) {
    writer.writeMessage(
      9,
      f,
      proto.kwaipilot.ToolParamsSchema.serializeBinaryToWriter
    );
  }
  f = message.getOutParamSchema();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getAuthConfig();
  if (f != null) {
    writer.writeMessage(
      11,
      f,
      proto.kwaipilot.ToolAuthConfig.serializeBinaryToWriter
    );
  }
};


/**
 * optional int64 id = 1;
 * @return {number}
 */
proto.kwaipilot.ToolDetailResponse.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.ToolDetailResponse} returns this
 */
proto.kwaipilot.ToolDetailResponse.prototype.setId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string tuid = 2;
 * @return {string}
 */
proto.kwaipilot.ToolDetailResponse.prototype.getTuid = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolDetailResponse} returns this
 */
proto.kwaipilot.ToolDetailResponse.prototype.setTuid = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string name = 3;
 * @return {string}
 */
proto.kwaipilot.ToolDetailResponse.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolDetailResponse} returns this
 */
proto.kwaipilot.ToolDetailResponse.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string icon = 4;
 * @return {string}
 */
proto.kwaipilot.ToolDetailResponse.prototype.getIcon = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolDetailResponse} returns this
 */
proto.kwaipilot.ToolDetailResponse.prototype.setIcon = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string description = 5;
 * @return {string}
 */
proto.kwaipilot.ToolDetailResponse.prototype.getDescription = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolDetailResponse} returns this
 */
proto.kwaipilot.ToolDetailResponse.prototype.setDescription = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string code = 6;
 * @return {string}
 */
proto.kwaipilot.ToolDetailResponse.prototype.getCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolDetailResponse} returns this
 */
proto.kwaipilot.ToolDetailResponse.prototype.setCode = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string api_method = 7;
 * @return {string}
 */
proto.kwaipilot.ToolDetailResponse.prototype.getApiMethod = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolDetailResponse} returns this
 */
proto.kwaipilot.ToolDetailResponse.prototype.setApiMethod = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string api_path = 8;
 * @return {string}
 */
proto.kwaipilot.ToolDetailResponse.prototype.getApiPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolDetailResponse} returns this
 */
proto.kwaipilot.ToolDetailResponse.prototype.setApiPath = function(value) {
  return jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional ToolParamsSchema enter_params_schema = 9;
 * @return {?proto.kwaipilot.ToolParamsSchema}
 */
proto.kwaipilot.ToolDetailResponse.prototype.getEnterParamsSchema = function() {
  return /** @type{?proto.kwaipilot.ToolParamsSchema} */ (
    jspb.Message.getWrapperField(this, proto.kwaipilot.ToolParamsSchema, 9));
};


/**
 * @param {?proto.kwaipilot.ToolParamsSchema|undefined} value
 * @return {!proto.kwaipilot.ToolDetailResponse} returns this
*/
proto.kwaipilot.ToolDetailResponse.prototype.setEnterParamsSchema = function(value) {
  return jspb.Message.setWrapperField(this, 9, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.kwaipilot.ToolDetailResponse} returns this
 */
proto.kwaipilot.ToolDetailResponse.prototype.clearEnterParamsSchema = function() {
  return this.setEnterParamsSchema(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.kwaipilot.ToolDetailResponse.prototype.hasEnterParamsSchema = function() {
  return jspb.Message.getField(this, 9) != null;
};


/**
 * optional string out_param_schema = 10;
 * @return {string}
 */
proto.kwaipilot.ToolDetailResponse.prototype.getOutParamSchema = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ToolDetailResponse} returns this
 */
proto.kwaipilot.ToolDetailResponse.prototype.setOutParamSchema = function(value) {
  return jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional ToolAuthConfig auth_config = 11;
 * @return {?proto.kwaipilot.ToolAuthConfig}
 */
proto.kwaipilot.ToolDetailResponse.prototype.getAuthConfig = function() {
  return /** @type{?proto.kwaipilot.ToolAuthConfig} */ (
    jspb.Message.getWrapperField(this, proto.kwaipilot.ToolAuthConfig, 11));
};


/**
 * @param {?proto.kwaipilot.ToolAuthConfig|undefined} value
 * @return {!proto.kwaipilot.ToolDetailResponse} returns this
*/
proto.kwaipilot.ToolDetailResponse.prototype.setAuthConfig = function(value) {
  return jspb.Message.setWrapperField(this, 11, value);
};


/**
 * Clears the message field making it undefined.
 * @return {!proto.kwaipilot.ToolDetailResponse} returns this
 */
proto.kwaipilot.ToolDetailResponse.prototype.clearAuthConfig = function() {
  return this.setAuthConfig(undefined);
};


/**
 * Returns whether this field is set.
 * @return {boolean}
 */
proto.kwaipilot.ToolDetailResponse.prototype.hasAuthConfig = function() {
  return jspb.Message.getField(this, 11) != null;
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.ChatFile.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.ChatFile.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.ChatFile} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ChatFile.toObject = function(includeInstance, msg) {
  var f, obj = {
    code: jspb.Message.getFieldWithDefault(msg, 1, ""),
    name: jspb.Message.getFieldWithDefault(msg, 2, ""),
    language: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.ChatFile}
 */
proto.kwaipilot.ChatFile.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.ChatFile;
  return proto.kwaipilot.ChatFile.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.ChatFile} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.ChatFile}
 */
proto.kwaipilot.ChatFile.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setLanguage(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.ChatFile.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.ChatFile.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.ChatFile} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.ChatFile.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCode();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getLanguage();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string code = 1;
 * @return {string}
 */
proto.kwaipilot.ChatFile.prototype.getCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ChatFile} returns this
 */
proto.kwaipilot.ChatFile.prototype.setCode = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string name = 2;
 * @return {string}
 */
proto.kwaipilot.ChatFile.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ChatFile} returns this
 */
proto.kwaipilot.ChatFile.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string language = 3;
 * @return {string}
 */
proto.kwaipilot.ChatFile.prototype.getLanguage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.ChatFile} returns this
 */
proto.kwaipilot.ChatFile.prototype.setLanguage = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.CodeSearchData.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.CodeSearchData.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.CodeSearchData} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.CodeSearchData.toObject = function(includeInstance, msg) {
  var f, obj = {
    id: jspb.Message.getFieldWithDefault(msg, 1, 0),
    path: jspb.Message.getFieldWithDefault(msg, 2, ""),
    startLineNo: jspb.Message.getFieldWithDefault(msg, 3, 0),
    endLineNo: jspb.Message.getFieldWithDefault(msg, 4, 0),
    startColNo: jspb.Message.getFieldWithDefault(msg, 5, 0),
    endColNo: jspb.Message.getFieldWithDefault(msg, 6, 0),
    code: jspb.Message.getFieldWithDefault(msg, 7, ""),
    language: jspb.Message.getFieldWithDefault(msg, 8, ""),
    functionName: jspb.Message.getFieldWithDefault(msg, 9, ""),
    functionSignature: jspb.Message.getFieldWithDefault(msg, 10, ""),
    codeType: jspb.Message.getFieldWithDefault(msg, 11, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.CodeSearchData}
 */
proto.kwaipilot.CodeSearchData.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.CodeSearchData;
  return proto.kwaipilot.CodeSearchData.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.CodeSearchData} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.CodeSearchData}
 */
proto.kwaipilot.CodeSearchData.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setPath(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setStartLineNo(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setEndLineNo(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setStartColNo(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setEndColNo(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setCode(value);
      break;
    case 8:
      var value = /** @type {string} */ (reader.readString());
      msg.setLanguage(value);
      break;
    case 9:
      var value = /** @type {string} */ (reader.readString());
      msg.setFunctionName(value);
      break;
    case 10:
      var value = /** @type {string} */ (reader.readString());
      msg.setFunctionSignature(value);
      break;
    case 11:
      var value = /** @type {string} */ (reader.readString());
      msg.setCodeType(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.CodeSearchData.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.CodeSearchData.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.CodeSearchData} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.CodeSearchData.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getId();
  if (f !== 0) {
    writer.writeInt64(
      1,
      f
    );
  }
  f = message.getPath();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getStartLineNo();
  if (f !== 0) {
    writer.writeInt64(
      3,
      f
    );
  }
  f = message.getEndLineNo();
  if (f !== 0) {
    writer.writeInt64(
      4,
      f
    );
  }
  f = message.getStartColNo();
  if (f !== 0) {
    writer.writeInt64(
      5,
      f
    );
  }
  f = message.getEndColNo();
  if (f !== 0) {
    writer.writeInt64(
      6,
      f
    );
  }
  f = message.getCode();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getLanguage();
  if (f.length > 0) {
    writer.writeString(
      8,
      f
    );
  }
  f = message.getFunctionName();
  if (f.length > 0) {
    writer.writeString(
      9,
      f
    );
  }
  f = message.getFunctionSignature();
  if (f.length > 0) {
    writer.writeString(
      10,
      f
    );
  }
  f = message.getCodeType();
  if (f.length > 0) {
    writer.writeString(
      11,
      f
    );
  }
};


/**
 * optional int64 id = 1;
 * @return {number}
 */
proto.kwaipilot.CodeSearchData.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 1, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.CodeSearchData} returns this
 */
proto.kwaipilot.CodeSearchData.prototype.setId = function(value) {
  return jspb.Message.setProto3IntField(this, 1, value);
};


/**
 * optional string path = 2;
 * @return {string}
 */
proto.kwaipilot.CodeSearchData.prototype.getPath = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.CodeSearchData} returns this
 */
proto.kwaipilot.CodeSearchData.prototype.setPath = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int64 start_line_no = 3;
 * @return {number}
 */
proto.kwaipilot.CodeSearchData.prototype.getStartLineNo = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.CodeSearchData} returns this
 */
proto.kwaipilot.CodeSearchData.prototype.setStartLineNo = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional int64 end_line_no = 4;
 * @return {number}
 */
proto.kwaipilot.CodeSearchData.prototype.getEndLineNo = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.CodeSearchData} returns this
 */
proto.kwaipilot.CodeSearchData.prototype.setEndLineNo = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int64 start_col_no = 5;
 * @return {number}
 */
proto.kwaipilot.CodeSearchData.prototype.getStartColNo = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.CodeSearchData} returns this
 */
proto.kwaipilot.CodeSearchData.prototype.setStartColNo = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional int64 end_col_no = 6;
 * @return {number}
 */
proto.kwaipilot.CodeSearchData.prototype.getEndColNo = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.CodeSearchData} returns this
 */
proto.kwaipilot.CodeSearchData.prototype.setEndColNo = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};


/**
 * optional string code = 7;
 * @return {string}
 */
proto.kwaipilot.CodeSearchData.prototype.getCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.CodeSearchData} returns this
 */
proto.kwaipilot.CodeSearchData.prototype.setCode = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional string language = 8;
 * @return {string}
 */
proto.kwaipilot.CodeSearchData.prototype.getLanguage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 8, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.CodeSearchData} returns this
 */
proto.kwaipilot.CodeSearchData.prototype.setLanguage = function(value) {
  return jspb.Message.setProto3StringField(this, 8, value);
};


/**
 * optional string function_name = 9;
 * @return {string}
 */
proto.kwaipilot.CodeSearchData.prototype.getFunctionName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 9, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.CodeSearchData} returns this
 */
proto.kwaipilot.CodeSearchData.prototype.setFunctionName = function(value) {
  return jspb.Message.setProto3StringField(this, 9, value);
};


/**
 * optional string function_signature = 10;
 * @return {string}
 */
proto.kwaipilot.CodeSearchData.prototype.getFunctionSignature = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 10, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.CodeSearchData} returns this
 */
proto.kwaipilot.CodeSearchData.prototype.setFunctionSignature = function(value) {
  return jspb.Message.setProto3StringField(this, 10, value);
};


/**
 * optional string code_type = 11;
 * @return {string}
 */
proto.kwaipilot.CodeSearchData.prototype.getCodeType = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 11, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.CodeSearchData} returns this
 */
proto.kwaipilot.CodeSearchData.prototype.setCodeType = function(value) {
  return jspb.Message.setProto3StringField(this, 11, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.CodeSearchRequest.repeatedFields_ = [3,5];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.CodeSearchRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.CodeSearchRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.CodeSearchRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.CodeSearchRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    repoName: jspb.Message.getFieldWithDefault(msg, 1, ""),
    commitId: jspb.Message.getFieldWithDefault(msg, 2, ""),
    targetDirectoryList: (f = jspb.Message.getRepeatedField(msg, 3)) == null ? undefined : f,
    query: jspb.Message.getFieldWithDefault(msg, 4, ""),
    filesList: jspb.Message.toObjectList(msg.getFilesList(),
    proto.kwaipilot.ChatFile.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.CodeSearchRequest}
 */
proto.kwaipilot.CodeSearchRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.CodeSearchRequest;
  return proto.kwaipilot.CodeSearchRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.CodeSearchRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.CodeSearchRequest}
 */
proto.kwaipilot.CodeSearchRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setRepoName(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setCommitId(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.addTargetDirectory(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setQuery(value);
      break;
    case 5:
      var value = new proto.kwaipilot.ChatFile;
      reader.readMessage(value,proto.kwaipilot.ChatFile.deserializeBinaryFromReader);
      msg.addFiles(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.CodeSearchRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.CodeSearchRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.CodeSearchRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.CodeSearchRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRepoName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getCommitId();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTargetDirectoryList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      3,
      f
    );
  }
  f = message.getQuery();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getFilesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      5,
      f,
      proto.kwaipilot.ChatFile.serializeBinaryToWriter
    );
  }
};


/**
 * optional string repo_name = 1;
 * @return {string}
 */
proto.kwaipilot.CodeSearchRequest.prototype.getRepoName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.CodeSearchRequest} returns this
 */
proto.kwaipilot.CodeSearchRequest.prototype.setRepoName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string commit_id = 2;
 * @return {string}
 */
proto.kwaipilot.CodeSearchRequest.prototype.getCommitId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.CodeSearchRequest} returns this
 */
proto.kwaipilot.CodeSearchRequest.prototype.setCommitId = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated string target_directory = 3;
 * @return {!Array<string>}
 */
proto.kwaipilot.CodeSearchRequest.prototype.getTargetDirectoryList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 3));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.kwaipilot.CodeSearchRequest} returns this
 */
proto.kwaipilot.CodeSearchRequest.prototype.setTargetDirectoryList = function(value) {
  return jspb.Message.setField(this, 3, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.CodeSearchRequest} returns this
 */
proto.kwaipilot.CodeSearchRequest.prototype.addTargetDirectory = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.CodeSearchRequest} returns this
 */
proto.kwaipilot.CodeSearchRequest.prototype.clearTargetDirectoryList = function() {
  return this.setTargetDirectoryList([]);
};


/**
 * optional string query = 4;
 * @return {string}
 */
proto.kwaipilot.CodeSearchRequest.prototype.getQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.CodeSearchRequest} returns this
 */
proto.kwaipilot.CodeSearchRequest.prototype.setQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * repeated ChatFile files = 5;
 * @return {!Array<!proto.kwaipilot.ChatFile>}
 */
proto.kwaipilot.CodeSearchRequest.prototype.getFilesList = function() {
  return /** @type{!Array<!proto.kwaipilot.ChatFile>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.ChatFile, 5));
};


/**
 * @param {!Array<!proto.kwaipilot.ChatFile>} value
 * @return {!proto.kwaipilot.CodeSearchRequest} returns this
*/
proto.kwaipilot.CodeSearchRequest.prototype.setFilesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 5, value);
};


/**
 * @param {!proto.kwaipilot.ChatFile=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.ChatFile}
 */
proto.kwaipilot.CodeSearchRequest.prototype.addFiles = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 5, opt_value, proto.kwaipilot.ChatFile, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.CodeSearchRequest} returns this
 */
proto.kwaipilot.CodeSearchRequest.prototype.clearFilesList = function() {
  return this.setFilesList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.CodeSearchResponse.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.CodeSearchResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.CodeSearchResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.CodeSearchResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.CodeSearchResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    listList: jspb.Message.toObjectList(msg.getListList(),
    proto.kwaipilot.CodeSearchData.toObject, includeInstance),
    prompt: jspb.Message.getFieldWithDefault(msg, 2, ""),
    total: jspb.Message.getFieldWithDefault(msg, 3, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.CodeSearchResponse}
 */
proto.kwaipilot.CodeSearchResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.CodeSearchResponse;
  return proto.kwaipilot.CodeSearchResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.CodeSearchResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.CodeSearchResponse}
 */
proto.kwaipilot.CodeSearchResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.kwaipilot.CodeSearchData;
      reader.readMessage(value,proto.kwaipilot.CodeSearchData.deserializeBinaryFromReader);
      msg.addList(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setPrompt(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setTotal(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.CodeSearchResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.CodeSearchResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.CodeSearchResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.CodeSearchResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getListList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.kwaipilot.CodeSearchData.serializeBinaryToWriter
    );
  }
  f = message.getPrompt();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTotal();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
};


/**
 * repeated CodeSearchData list = 1;
 * @return {!Array<!proto.kwaipilot.CodeSearchData>}
 */
proto.kwaipilot.CodeSearchResponse.prototype.getListList = function() {
  return /** @type{!Array<!proto.kwaipilot.CodeSearchData>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.CodeSearchData, 1));
};


/**
 * @param {!Array<!proto.kwaipilot.CodeSearchData>} value
 * @return {!proto.kwaipilot.CodeSearchResponse} returns this
*/
proto.kwaipilot.CodeSearchResponse.prototype.setListList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.kwaipilot.CodeSearchData=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.CodeSearchData}
 */
proto.kwaipilot.CodeSearchResponse.prototype.addList = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.kwaipilot.CodeSearchData, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.CodeSearchResponse} returns this
 */
proto.kwaipilot.CodeSearchResponse.prototype.clearListList = function() {
  return this.setListList([]);
};


/**
 * optional string prompt = 2;
 * @return {string}
 */
proto.kwaipilot.CodeSearchResponse.prototype.getPrompt = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.CodeSearchResponse} returns this
 */
proto.kwaipilot.CodeSearchResponse.prototype.setPrompt = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional int32 total = 3;
 * @return {number}
 */
proto.kwaipilot.CodeSearchResponse.prototype.getTotal = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.CodeSearchResponse} returns this
 */
proto.kwaipilot.CodeSearchResponse.prototype.setTotal = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


goog.object.extend(exports, proto.kwaipilot);
