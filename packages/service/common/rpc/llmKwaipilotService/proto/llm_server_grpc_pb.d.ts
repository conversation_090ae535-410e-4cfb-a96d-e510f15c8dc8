// package: kwaipilot
// file: llm_server.proto

/* tslint:disable */
/* eslint-disable */

import * as grpc from '@infra-node/grpc';
import * as llm_server_pb from './llm_server_pb';
import type { Metadata, RPCCallOptions } from '@infra-node/rpc';

export interface ILlmServerServiceClient {
  listChatModels(
    request: llm_server_pb.ChatModelListQuery,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ChatModelListResponse
    ) => void
  ): void;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ChatModelListResponse
    ) => void
  ): void;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ChatModelListResponse
    ) => void
  ): void;
  listAgents(
    request: llm_server_pb.AgentModelListQuery,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.AgentModelListResponse
    ) => void
  ): void;
  listAgents(
    request: llm_server_pb.AgentModelListQuery,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.AgentModelListResponse
    ) => void
  ): void;
  listAgents(
    request: llm_server_pb.AgentModelListQuery,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.AgentModelListResponse
    ) => void
  ): void;
  knowledgeRepoSearch(
    request: llm_server_pb.KnowledgeRepoSearchRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearch(
    request: llm_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearch(
    request: llm_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearchForDataAsset(
    request: llm_server_pb.KnowledgeRepoSearchRequest,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearchForDataAsset(
    request: llm_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  knowledgeRepoSearchForDataAsset(
    request: llm_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoSearchResponse
    ) => void
  ): void;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest,
    callback: (error: grpc.ServiceError | null, response: llm_server_pb.ToolDetailResponse) => void
  ): void;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest,
    metadata: Metadata,
    callback: (error: grpc.ServiceError | null, response: llm_server_pb.ToolDetailResponse) => void
  ): void;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (error: grpc.ServiceError | null, response: llm_server_pb.ToolDetailResponse) => void
  ): void;
  codeSearch(
    request: llm_server_pb.CodeSearchRequest,
    callback: (error: grpc.ServiceError | null, response: llm_server_pb.CodeSearchResponse) => void
  ): void;
  codeSearch(
    request: llm_server_pb.CodeSearchRequest,
    metadata: Metadata,
    callback: (error: grpc.ServiceError | null, response: llm_server_pb.CodeSearchResponse) => void
  ): void;
  codeSearch(
    request: llm_server_pb.CodeSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (error: grpc.ServiceError | null, response: llm_server_pb.CodeSearchResponse) => void
  ): void;
}

export interface ILlmServerServiceClientPromisify {
  listChatModels(
    request: llm_server_pb.ChatModelListQuery
  ): Promise<llm_server_pb.ChatModelListResponse>;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery,
    metadata: Metadata
  ): Promise<llm_server_pb.ChatModelListResponse>;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.ChatModelListResponse>;
  listAgents(
    request: llm_server_pb.AgentModelListQuery
  ): Promise<llm_server_pb.AgentModelListResponse>;
  listAgents(
    request: llm_server_pb.AgentModelListQuery,
    metadata: Metadata
  ): Promise<llm_server_pb.AgentModelListResponse>;
  listAgents(
    request: llm_server_pb.AgentModelListQuery,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.AgentModelListResponse>;
  knowledgeRepoSearch(
    request: llm_server_pb.KnowledgeRepoSearchRequest
  ): Promise<llm_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearch(
    request: llm_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata
  ): Promise<llm_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearch(
    request: llm_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearchForDataAsset(
    request: llm_server_pb.KnowledgeRepoSearchRequest
  ): Promise<llm_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearchForDataAsset(
    request: llm_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata
  ): Promise<llm_server_pb.KnowledgeRepoSearchResponse>;
  knowledgeRepoSearchForDataAsset(
    request: llm_server_pb.KnowledgeRepoSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.KnowledgeRepoSearchResponse>;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest
  ): Promise<llm_server_pb.ToolDetailResponse>;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest,
    metadata: Metadata
  ): Promise<llm_server_pb.ToolDetailResponse>;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.ToolDetailResponse>;
  codeSearch(request: llm_server_pb.CodeSearchRequest): Promise<llm_server_pb.CodeSearchResponse>;
  codeSearch(
    request: llm_server_pb.CodeSearchRequest,
    metadata: Metadata
  ): Promise<llm_server_pb.CodeSearchResponse>;
  codeSearch(
    request: llm_server_pb.CodeSearchRequest,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.CodeSearchResponse>;
}

export interface ILlmServerServiceClientDynamic {
  listChatModels(
    request: llm_server_pb.ChatModelListQuery.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ChatModelListResponse.DynamicObject
    ) => void
  ): void;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ChatModelListResponse.DynamicObject
    ) => void
  ): void;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ChatModelListResponse.DynamicObject
    ) => void
  ): void;
  listAgents(
    request: llm_server_pb.AgentModelListQuery.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.AgentModelListResponse.DynamicObject
    ) => void
  ): void;
  listAgents(
    request: llm_server_pb.AgentModelListQuery.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.AgentModelListResponse.DynamicObject
    ) => void
  ): void;
  listAgents(
    request: llm_server_pb.AgentModelListQuery.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.AgentModelListResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearch(
    request: llm_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearch(
    request: llm_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearch(
    request: llm_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearchForDataAsset(
    request: llm_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearchForDataAsset(
    request: llm_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  knowledgeRepoSearchForDataAsset(
    request: llm_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.KnowledgeRepoSearchResponse.DynamicObject
    ) => void
  ): void;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ToolDetailResponse.DynamicObject
    ) => void
  ): void;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ToolDetailResponse.DynamicObject
    ) => void
  ): void;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.ToolDetailResponse.DynamicObject
    ) => void
  ): void;
  codeSearch(
    request: llm_server_pb.CodeSearchRequest.DynamicObject,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.CodeSearchResponse.DynamicObject
    ) => void
  ): void;
  codeSearch(
    request: llm_server_pb.CodeSearchRequest.DynamicObject,
    metadata: Metadata,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.CodeSearchResponse.DynamicObject
    ) => void
  ): void;
  codeSearch(
    request: llm_server_pb.CodeSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions,
    callback: (
      error: grpc.ServiceError | null,
      response: llm_server_pb.CodeSearchResponse.DynamicObject
    ) => void
  ): void;
}

export interface ILlmServerServiceClientDynamicPromisify {
  listChatModels(
    request: llm_server_pb.ChatModelListQuery.DynamicObject
  ): Promise<llm_server_pb.ChatModelListResponse.DynamicObject>;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery.DynamicObject,
    metadata: Metadata
  ): Promise<llm_server_pb.ChatModelListResponse.DynamicObject>;
  listChatModels(
    request: llm_server_pb.ChatModelListQuery.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.ChatModelListResponse.DynamicObject>;
  listAgents(
    request: llm_server_pb.AgentModelListQuery.DynamicObject
  ): Promise<llm_server_pb.AgentModelListResponse.DynamicObject>;
  listAgents(
    request: llm_server_pb.AgentModelListQuery.DynamicObject,
    metadata: Metadata
  ): Promise<llm_server_pb.AgentModelListResponse.DynamicObject>;
  listAgents(
    request: llm_server_pb.AgentModelListQuery.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.AgentModelListResponse.DynamicObject>;
  knowledgeRepoSearch(
    request: llm_server_pb.KnowledgeRepoSearchRequest.DynamicObject
  ): Promise<llm_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearch(
    request: llm_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearch(
    request: llm_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearchForDataAsset(
    request: llm_server_pb.KnowledgeRepoSearchRequest.DynamicObject
  ): Promise<llm_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearchForDataAsset(
    request: llm_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  knowledgeRepoSearchForDataAsset(
    request: llm_server_pb.KnowledgeRepoSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.KnowledgeRepoSearchResponse.DynamicObject>;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest.DynamicObject
  ): Promise<llm_server_pb.ToolDetailResponse.DynamicObject>;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_server_pb.ToolDetailResponse.DynamicObject>;
  getToolDetail(
    request: llm_server_pb.ToolDetailRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.ToolDetailResponse.DynamicObject>;
  codeSearch(
    request: llm_server_pb.CodeSearchRequest.DynamicObject
  ): Promise<llm_server_pb.CodeSearchResponse.DynamicObject>;
  codeSearch(
    request: llm_server_pb.CodeSearchRequest.DynamicObject,
    metadata: Metadata
  ): Promise<llm_server_pb.CodeSearchResponse.DynamicObject>;
  codeSearch(
    request: llm_server_pb.CodeSearchRequest.DynamicObject,
    metadata: Metadata,
    options: RPCCallOptions
  ): Promise<llm_server_pb.CodeSearchResponse.DynamicObject>;
}

export interface IConsumer {
  LlmServerService: ILlmServerServiceClient;
  __promise__: {
    LlmServerService: ILlmServerServiceClientPromisify;
  };
}

export interface IConsumerDynamic {
  LlmServerService: ILlmServerServiceClientDynamic;
  __promise__: {
    LlmServerService: ILlmServerServiceClientDynamicPromisify;
  };
}
