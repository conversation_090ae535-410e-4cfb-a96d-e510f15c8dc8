// package: kwaipilot
// file: llm_server.proto

/* tslint:disable */
/* eslint-disable */

import * as jspb from 'google-protobuf';

export class ChatModelListQuery extends jspb.Message {
  getUsername(): string;
  setUsername(value: string): ChatModelListQuery;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ChatModelListQuery.AsObject;
  static toObject(includeInstance: boolean, msg: ChatModelListQuery): ChatModelListQuery.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: ChatModelListQuery, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ChatModelListQuery;
  static deserializeBinaryFromReader(
    message: Chat<PERSON><PERSON>lListQuer<PERSON>,
    reader: jspb.BinaryReader
  ): ChatModelListQuery;
}

export namespace ChatModelListQuery {
  export type AsObject = {
    username: string;
  };

  export type DynamicObject = {
    username: string;
  };
}

export class ChatModel extends jspb.Message {
  getCode(): string;
  setCode(value: string): ChatModel;
  getName(): string;
  setName(value: string): ChatModel;
  getDesc(): string;
  setDesc(value: string): ChatModel;
  getIcon(): string;
  setIcon(value: string): ChatModel;
  getDisable(): boolean;
  setDisable(value: boolean): ChatModel;
  getMaxInputLength(): number;
  setMaxInputLength(value: number): ChatModel;
  getDisableIcon(): string;
  setDisableIcon(value: string): ChatModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ChatModel.AsObject;
  static toObject(includeInstance: boolean, msg: ChatModel): ChatModel.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: ChatModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ChatModel;
  static deserializeBinaryFromReader(message: ChatModel, reader: jspb.BinaryReader): ChatModel;
}

export namespace ChatModel {
  export type AsObject = {
    code: string;
    name: string;
    desc: string;
    icon: string;
    disable: boolean;
    maxInputLength: number;
    disableIcon: string;
  };

  export type DynamicObject = {
    code: string;
    name: string;
    desc: string;
    icon: string;
    disable: boolean;
    maxInputLength: number;
    disableIcon: string;
  };
}

export class UserInfo extends jspb.Message {
  getUsername(): string;
  setUsername(value: string): UserInfo;
  getAvatarUrl(): string;
  setAvatarUrl(value: string): UserInfo;
  getName(): string;
  setName(value: string): UserInfo;
  getDept1(): string;
  setDept1(value: string): UserInfo;
  getDeptCode(): string;
  setDeptCode(value: string): UserInfo;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): UserInfo.AsObject;
  static toObject(includeInstance: boolean, msg: UserInfo): UserInfo.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: UserInfo, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): UserInfo;
  static deserializeBinaryFromReader(message: UserInfo, reader: jspb.BinaryReader): UserInfo;
}

export namespace UserInfo {
  export type AsObject = {
    username: string;
    avatarUrl: string;
    name: string;
    dept1: string;
    deptCode: string;
  };

  export type DynamicObject = {
    username: string;
    avatarUrl: string;
    name: string;
    dept1: string;
    deptCode: string;
  };
}

export class ChatModelListResponse extends jspb.Message {
  clearChatModelsList(): void;
  getChatModelsList(): Array<ChatModel>;
  setChatModelsList(value: Array<ChatModel>): ChatModelListResponse;
  addChatModels(value?: ChatModel, index?: number): ChatModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ChatModelListResponse.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: ChatModelListResponse
  ): ChatModelListResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: ChatModelListResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ChatModelListResponse;
  static deserializeBinaryFromReader(
    message: ChatModelListResponse,
    reader: jspb.BinaryReader
  ): ChatModelListResponse;
}

export namespace ChatModelListResponse {
  export type AsObject = {
    chatModelsList: Array<ChatModel.AsObject>;
  };

  export type DynamicObject = {
    chatModels: Array<ChatModel.DynamicObject>;
  };
}

export class AgentModelListQuery extends jspb.Message {
  getLimit(): number;
  setLimit(value: number): AgentModelListQuery;
  getOffset(): number;
  setOffset(value: number): AgentModelListQuery;
  getName(): string;
  setName(value: string): AgentModelListQuery;
  getOrderType(): number;
  setOrderType(value: number): AgentModelListQuery;
  getUsername(): string;
  setUsername(value: string): AgentModelListQuery;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): AgentModelListQuery.AsObject;
  static toObject(includeInstance: boolean, msg: AgentModelListQuery): AgentModelListQuery.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: AgentModelListQuery, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): AgentModelListQuery;
  static deserializeBinaryFromReader(
    message: AgentModelListQuery,
    reader: jspb.BinaryReader
  ): AgentModelListQuery;
}

export namespace AgentModelListQuery {
  export type AsObject = {
    limit: number;
    offset: number;
    name: string;
    orderType: number;
    username: string;
  };

  export type DynamicObject = {
    limit: number;
    offset: number;
    name: string;
    orderType: number;
    username: string;
  };
}

export class AgentModel extends jspb.Message {
  getAuid(): string;
  setAuid(value: string): AgentModel;
  getCreateTime(): number;
  setCreateTime(value: number): AgentModel;
  getDescription(): string;
  setDescription(value: string): AgentModel;
  getIcon(): string;
  setIcon(value: string): AgentModel;
  getId(): number;
  setId(value: number): AgentModel;
  getLocked(): boolean;
  setLocked(value: boolean): AgentModel;
  clearMaintainerListList(): void;
  getMaintainerListList(): Array<UserInfo>;
  setMaintainerListList(value: Array<UserInfo>): AgentModel;
  addMaintainerList(value?: UserInfo, index?: number): UserInfo;
  getName(): string;
  setName(value: string): AgentModel;
  getOfficial(): boolean;
  setOfficial(value: boolean): AgentModel;
  getParentAgentId(): number;
  setParentAgentId(value: number): AgentModel;
  getRefCount(): number;
  setRefCount(value: number): AgentModel;
  getRoleDescription(): string;
  setRoleDescription(value: string): AgentModel;
  getStatus(): string;
  setStatus(value: string): AgentModel;
  getUpdateTime(): number;
  setUpdateTime(value: number): AgentModel;

  hasUpdater(): boolean;
  clearUpdater(): void;
  getUpdater(): UserInfo | undefined;
  setUpdater(value?: UserInfo): AgentModel;
  getVisibility(): string;
  setVisibility(value: string): AgentModel;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): AgentModel.AsObject;
  static toObject(includeInstance: boolean, msg: AgentModel): AgentModel.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: AgentModel, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): AgentModel;
  static deserializeBinaryFromReader(message: AgentModel, reader: jspb.BinaryReader): AgentModel;
}

export namespace AgentModel {
  export type AsObject = {
    auid: string;
    createTime: number;
    description: string;
    icon: string;
    id: number;
    locked: boolean;
    maintainerListList: Array<UserInfo.AsObject>;
    name: string;
    official: boolean;
    parentAgentId: number;
    refCount: number;
    roleDescription: string;
    status: string;
    updateTime: number;
    updater?: UserInfo.AsObject;
    visibility: string;
  };

  export type DynamicObject = {
    auid: string;
    createTime: string;
    description: string;
    icon: string;
    id: string;
    locked: boolean;
    maintainerList: Array<UserInfo.DynamicObject>;
    name: string;
    official: boolean;
    parentAgentId: string;
    refCount: string;
    roleDescription: string;
    status: string;
    updateTime: string;
    updater?: UserInfo.DynamicObject;
    visibility: string;
  };
}

export class AgentModelListResponse extends jspb.Message {
  clearListList(): void;
  getListList(): Array<AgentModel>;
  setListList(value: Array<AgentModel>): AgentModelListResponse;
  addList(value?: AgentModel, index?: number): AgentModel;
  getTotal(): number;
  setTotal(value: number): AgentModelListResponse;
  getPageNum(): number;
  setPageNum(value: number): AgentModelListResponse;
  getPageSize(): number;
  setPageSize(value: number): AgentModelListResponse;
  getPages(): number;
  setPages(value: number): AgentModelListResponse;
  getHasMore(): boolean;
  setHasMore(value: boolean): AgentModelListResponse;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): AgentModelListResponse.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: AgentModelListResponse
  ): AgentModelListResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: AgentModelListResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): AgentModelListResponse;
  static deserializeBinaryFromReader(
    message: AgentModelListResponse,
    reader: jspb.BinaryReader
  ): AgentModelListResponse;
}

export namespace AgentModelListResponse {
  export type AsObject = {
    listList: Array<AgentModel.AsObject>;
    total: number;
    pageNum: number;
    pageSize: number;
    pages: number;
    hasMore: boolean;
  };

  export type DynamicObject = {
    list: Array<AgentModel.DynamicObject>;
    total: string;
    pageNum: number;
    pageSize: number;
    pages: number;
    hasMore: boolean;
  };
}

export class KnowledgeRepoSearchRequest extends jspb.Message {
  getPlatform(): string;
  setPlatform(value: string): KnowledgeRepoSearchRequest;
  clearKnowledgeRepoIdsList(): void;
  getKnowledgeRepoIdsList(): Array<number>;
  setKnowledgeRepoIdsList(value: Array<number>): KnowledgeRepoSearchRequest;
  addKnowledgeRepoIds(value: number, index?: number): number;
  getQuery(): string;
  setQuery(value: string): KnowledgeRepoSearchRequest;
  getTopK(): number;
  setTopK(value: number): KnowledgeRepoSearchRequest;
  clearChatHistoryList(): void;
  getChatHistoryList(): Array<ChatMessage>;
  setChatHistoryList(value: Array<ChatMessage>): KnowledgeRepoSearchRequest;
  addChatHistory(value?: ChatMessage, index?: number): ChatMessage;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KnowledgeRepoSearchRequest.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KnowledgeRepoSearchRequest
  ): KnowledgeRepoSearchRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KnowledgeRepoSearchRequest,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KnowledgeRepoSearchRequest;
  static deserializeBinaryFromReader(
    message: KnowledgeRepoSearchRequest,
    reader: jspb.BinaryReader
  ): KnowledgeRepoSearchRequest;
}

export namespace KnowledgeRepoSearchRequest {
  export type AsObject = {
    platform: string;
    knowledgeRepoIdsList: Array<number>;
    query: string;
    topK: number;
    chatHistoryList: Array<ChatMessage.AsObject>;
  };

  export type DynamicObject = {
    platform: string;
    knowledgeRepoIds: Array<string>;
    query: string;
    topK: number;
    chatHistory: Array<ChatMessage.DynamicObject>;
  };
}

export class ChatMessage extends jspb.Message {
  getRole(): string;
  setRole(value: string): ChatMessage;
  getContent(): string;
  setContent(value: string): ChatMessage;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ChatMessage.AsObject;
  static toObject(includeInstance: boolean, msg: ChatMessage): ChatMessage.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: ChatMessage, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ChatMessage;
  static deserializeBinaryFromReader(message: ChatMessage, reader: jspb.BinaryReader): ChatMessage;
}

export namespace ChatMessage {
  export type AsObject = {
    role: string;
    content: string;
  };

  export type DynamicObject = {
    role: string;
    content: string;
  };
}

export class Knowledge extends jspb.Message {
  getContent(): string;
  setContent(value: string): Knowledge;
  getLink(): string;
  setLink(value: string): Knowledge;
  getTitle(): string;
  setTitle(value: string): Knowledge;
  getKnowledgeRepoId(): string;
  setKnowledgeRepoId(value: string): Knowledge;
  getKnowledgeRepoName(): string;
  setKnowledgeRepoName(value: string): Knowledge;
  getScore(): number;
  setScore(value: number): Knowledge;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Knowledge.AsObject;
  static toObject(includeInstance: boolean, msg: Knowledge): Knowledge.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: Knowledge, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Knowledge;
  static deserializeBinaryFromReader(message: Knowledge, reader: jspb.BinaryReader): Knowledge;
}

export namespace Knowledge {
  export type AsObject = {
    content: string;
    link: string;
    title: string;
    knowledgeRepoId: string;
    knowledgeRepoName: string;
    score: number;
  };

  export type DynamicObject = {
    content: string;
    link: string;
    title: string;
    knowledgeRepoId: string;
    knowledgeRepoName: string;
    score: number;
  };
}

export class KnowledgeRepoSearchResponse extends jspb.Message {
  clearKnowledgesList(): void;
  getKnowledgesList(): Array<Knowledge>;
  setKnowledgesList(value: Array<Knowledge>): KnowledgeRepoSearchResponse;
  addKnowledges(value?: Knowledge, index?: number): Knowledge;
  getPrompt(): string;
  setPrompt(value: string): KnowledgeRepoSearchResponse;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KnowledgeRepoSearchResponse.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KnowledgeRepoSearchResponse
  ): KnowledgeRepoSearchResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KnowledgeRepoSearchResponse,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KnowledgeRepoSearchResponse;
  static deserializeBinaryFromReader(
    message: KnowledgeRepoSearchResponse,
    reader: jspb.BinaryReader
  ): KnowledgeRepoSearchResponse;
}

export namespace KnowledgeRepoSearchResponse {
  export type AsObject = {
    knowledgesList: Array<Knowledge.AsObject>;
    prompt: string;
  };

  export type DynamicObject = {
    knowledges: Array<Knowledge.DynamicObject>;
    prompt: string;
  };
}

export class ToolDetailRequest extends jspb.Message {
  getToolId(): number;
  setToolId(value: number): ToolDetailRequest;
  getTuid(): string;
  setTuid(value: string): ToolDetailRequest;
  getUsername(): string;
  setUsername(value: string): ToolDetailRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ToolDetailRequest.AsObject;
  static toObject(includeInstance: boolean, msg: ToolDetailRequest): ToolDetailRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: ToolDetailRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ToolDetailRequest;
  static deserializeBinaryFromReader(
    message: ToolDetailRequest,
    reader: jspb.BinaryReader
  ): ToolDetailRequest;
}

export namespace ToolDetailRequest {
  export type AsObject = {
    toolId: number;
    tuid: string;
    username: string;
  };

  export type DynamicObject = {
    toolId: string;
    tuid: string;
    username: string;
  };
}

export class ToolParamsSchema extends jspb.Message {
  getBodyParamsSchema(): string;
  setBodyParamsSchema(value: string): ToolParamsSchema;
  getQueryParamsSchema(): string;
  setQueryParamsSchema(value: string): ToolParamsSchema;
  getHeaderParamsSchema(): string;
  setHeaderParamsSchema(value: string): ToolParamsSchema;
  getPathParamsSchema(): string;
  setPathParamsSchema(value: string): ToolParamsSchema;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ToolParamsSchema.AsObject;
  static toObject(includeInstance: boolean, msg: ToolParamsSchema): ToolParamsSchema.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: ToolParamsSchema, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ToolParamsSchema;
  static deserializeBinaryFromReader(
    message: ToolParamsSchema,
    reader: jspb.BinaryReader
  ): ToolParamsSchema;
}

export namespace ToolParamsSchema {
  export type AsObject = {
    bodyParamsSchema: string;
    queryParamsSchema: string;
    headerParamsSchema: string;
    pathParamsSchema: string;
  };

  export type DynamicObject = {
    bodyParamsSchema: string;
    queryParamsSchema: string;
    headerParamsSchema: string;
    pathParamsSchema: string;
  };
}

export class ToolAuthConfig extends jspb.Message {
  getAuthType(): number;
  setAuthType(value: number): ToolAuthConfig;
  getLocation(): string;
  setLocation(value: string): ToolAuthConfig;
  getName(): string;
  setName(value: string): ToolAuthConfig;
  getValue(): string;
  setValue(value: string): ToolAuthConfig;
  getAppKey(): string;
  setAppKey(value: string): ToolAuthConfig;
  getSecretKey(): string;
  setSecretKey(value: string): ToolAuthConfig;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ToolAuthConfig.AsObject;
  static toObject(includeInstance: boolean, msg: ToolAuthConfig): ToolAuthConfig.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: ToolAuthConfig, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ToolAuthConfig;
  static deserializeBinaryFromReader(
    message: ToolAuthConfig,
    reader: jspb.BinaryReader
  ): ToolAuthConfig;
}

export namespace ToolAuthConfig {
  export type AsObject = {
    authType: number;
    location: string;
    name: string;
    value: string;
    appKey: string;
    secretKey: string;
  };

  export type DynamicObject = {
    authType: number;
    location: string;
    name: string;
    value: string;
    appKey: string;
    secretKey: string;
  };
}

export class ToolDetailResponse extends jspb.Message {
  getId(): number;
  setId(value: number): ToolDetailResponse;
  getTuid(): string;
  setTuid(value: string): ToolDetailResponse;
  getName(): string;
  setName(value: string): ToolDetailResponse;
  getIcon(): string;
  setIcon(value: string): ToolDetailResponse;
  getDescription(): string;
  setDescription(value: string): ToolDetailResponse;
  getCode(): string;
  setCode(value: string): ToolDetailResponse;
  getApiMethod(): string;
  setApiMethod(value: string): ToolDetailResponse;
  getApiPath(): string;
  setApiPath(value: string): ToolDetailResponse;

  hasEnterParamsSchema(): boolean;
  clearEnterParamsSchema(): void;
  getEnterParamsSchema(): ToolParamsSchema | undefined;
  setEnterParamsSchema(value?: ToolParamsSchema): ToolDetailResponse;
  getOutParamSchema(): string;
  setOutParamSchema(value: string): ToolDetailResponse;

  hasAuthConfig(): boolean;
  clearAuthConfig(): void;
  getAuthConfig(): ToolAuthConfig | undefined;
  setAuthConfig(value?: ToolAuthConfig): ToolDetailResponse;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ToolDetailResponse.AsObject;
  static toObject(includeInstance: boolean, msg: ToolDetailResponse): ToolDetailResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: ToolDetailResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ToolDetailResponse;
  static deserializeBinaryFromReader(
    message: ToolDetailResponse,
    reader: jspb.BinaryReader
  ): ToolDetailResponse;
}

export namespace ToolDetailResponse {
  export type AsObject = {
    id: number;
    tuid: string;
    name: string;
    icon: string;
    description: string;
    code: string;
    apiMethod: string;
    apiPath: string;
    enterParamsSchema?: ToolParamsSchema.AsObject;
    outParamSchema: string;
    authConfig?: ToolAuthConfig.AsObject;
  };

  export type DynamicObject = {
    id: string;
    tuid: string;
    name: string;
    icon: string;
    description: string;
    code: string;
    apiMethod: string;
    apiPath: string;
    enterParamsSchema?: ToolParamsSchema.DynamicObject;
    outParamSchema: string;
    authConfig?: ToolAuthConfig.DynamicObject;
  };
}

export class ChatFile extends jspb.Message {
  getCode(): string;
  setCode(value: string): ChatFile;
  getName(): string;
  setName(value: string): ChatFile;
  getLanguage(): string;
  setLanguage(value: string): ChatFile;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ChatFile.AsObject;
  static toObject(includeInstance: boolean, msg: ChatFile): ChatFile.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: ChatFile, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ChatFile;
  static deserializeBinaryFromReader(message: ChatFile, reader: jspb.BinaryReader): ChatFile;
}

export namespace ChatFile {
  export type AsObject = {
    code: string;
    name: string;
    language: string;
  };

  export type DynamicObject = {
    code: string;
    name: string;
    language: string;
  };
}

export class CodeSearchData extends jspb.Message {
  getId(): number;
  setId(value: number): CodeSearchData;
  getPath(): string;
  setPath(value: string): CodeSearchData;
  getStartLineNo(): number;
  setStartLineNo(value: number): CodeSearchData;
  getEndLineNo(): number;
  setEndLineNo(value: number): CodeSearchData;
  getStartColNo(): number;
  setStartColNo(value: number): CodeSearchData;
  getEndColNo(): number;
  setEndColNo(value: number): CodeSearchData;
  getCode(): string;
  setCode(value: string): CodeSearchData;
  getLanguage(): string;
  setLanguage(value: string): CodeSearchData;
  getFunctionName(): string;
  setFunctionName(value: string): CodeSearchData;
  getFunctionSignature(): string;
  setFunctionSignature(value: string): CodeSearchData;
  getCodeType(): string;
  setCodeType(value: string): CodeSearchData;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CodeSearchData.AsObject;
  static toObject(includeInstance: boolean, msg: CodeSearchData): CodeSearchData.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: CodeSearchData, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CodeSearchData;
  static deserializeBinaryFromReader(
    message: CodeSearchData,
    reader: jspb.BinaryReader
  ): CodeSearchData;
}

export namespace CodeSearchData {
  export type AsObject = {
    id: number;
    path: string;
    startLineNo: number;
    endLineNo: number;
    startColNo: number;
    endColNo: number;
    code: string;
    language: string;
    functionName: string;
    functionSignature: string;
    codeType: string;
  };

  export type DynamicObject = {
    id: string;
    path: string;
    startLineNo: string;
    endLineNo: string;
    startColNo: string;
    endColNo: string;
    code: string;
    language: string;
    functionName: string;
    functionSignature: string;
    codeType: string;
  };
}

export class CodeSearchRequest extends jspb.Message {
  getRepoName(): string;
  setRepoName(value: string): CodeSearchRequest;
  getCommitId(): string;
  setCommitId(value: string): CodeSearchRequest;
  clearTargetDirectoryList(): void;
  getTargetDirectoryList(): Array<string>;
  setTargetDirectoryList(value: Array<string>): CodeSearchRequest;
  addTargetDirectory(value: string, index?: number): string;
  getQuery(): string;
  setQuery(value: string): CodeSearchRequest;
  clearFilesList(): void;
  getFilesList(): Array<ChatFile>;
  setFilesList(value: Array<ChatFile>): CodeSearchRequest;
  addFiles(value?: ChatFile, index?: number): ChatFile;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CodeSearchRequest.AsObject;
  static toObject(includeInstance: boolean, msg: CodeSearchRequest): CodeSearchRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: CodeSearchRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CodeSearchRequest;
  static deserializeBinaryFromReader(
    message: CodeSearchRequest,
    reader: jspb.BinaryReader
  ): CodeSearchRequest;
}

export namespace CodeSearchRequest {
  export type AsObject = {
    repoName: string;
    commitId: string;
    targetDirectoryList: Array<string>;
    query: string;
    filesList: Array<ChatFile.AsObject>;
  };

  export type DynamicObject = {
    repoName: string;
    commitId: string;
    targetDirectory: Array<string>;
    query: string;
    files: Array<ChatFile.DynamicObject>;
  };
}

export class CodeSearchResponse extends jspb.Message {
  clearListList(): void;
  getListList(): Array<CodeSearchData>;
  setListList(value: Array<CodeSearchData>): CodeSearchResponse;
  addList(value?: CodeSearchData, index?: number): CodeSearchData;
  getPrompt(): string;
  setPrompt(value: string): CodeSearchResponse;
  getTotal(): number;
  setTotal(value: number): CodeSearchResponse;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CodeSearchResponse.AsObject;
  static toObject(includeInstance: boolean, msg: CodeSearchResponse): CodeSearchResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: CodeSearchResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CodeSearchResponse;
  static deserializeBinaryFromReader(
    message: CodeSearchResponse,
    reader: jspb.BinaryReader
  ): CodeSearchResponse;
}

export namespace CodeSearchResponse {
  export type AsObject = {
    listList: Array<CodeSearchData.AsObject>;
    prompt: string;
    total: number;
  };

  export type DynamicObject = {
    list: Array<CodeSearchData.DynamicObject>;
    prompt: string;
    total: number;
  };
}
