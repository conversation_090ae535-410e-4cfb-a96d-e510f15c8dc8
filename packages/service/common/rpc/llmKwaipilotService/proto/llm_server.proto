syntax = "proto3";

package kwaipilot;
option java_multiple_files = true;

message ChatModelListQuery {
  string username = 1;
}

message ChatModel {
  string code = 1;
  string name = 2;
  string desc = 3;
  string icon = 4;
  bool disable = 5;
  int32 max_input_length = 6;
  string disable_icon = 7;
}

message UserInfo {
  string username = 1;
  string avatar_url = 2;
  string name = 3;
  string dept1 = 4;
  string dept_code = 5;
}

message ChatModelListResponse {
  repeated ChatModel chat_models = 1;
}

message AgentModelListQuery {
  int32 limit = 1;
  int32 offset = 2;
  string name = 3;
  int32 order_type = 4;
  string username = 5;
}

message AgentModel {
  string auid = 1;
  int64 create_time = 2;
  string description = 3;
  string icon = 4;
  int64 id = 5;
  bool locked = 6;
  repeated UserInfo maintainer_list = 7;
  string name = 8;
  bool official = 9;
  int64 parent_agent_id = 10;
  int64 ref_count = 11;
  string role_description = 12;
  string status = 13;
  int64 update_time = 14;
  UserInfo updater = 15;
  string visibility = 16;
}

message AgentModelListResponse {
  repeated AgentModel list = 1;
  int64 total = 2;
  int32 page_num = 3;
  int32 page_size = 4;
  int32 pages = 5;
  bool has_more = 6;
}

message KnowledgeRepoSearchRequest {
  string platform = 1; // 请求来源平台，KwaipilotAgent
  repeated int64 knowledge_repo_ids = 2;
  string query = 3;
  int32 top_k = 4;
  repeated ChatMessage chat_history = 5;
}

message ChatMessage {
  string role = 1; // user or assistant
  string content = 2;
}

message Knowledge {
  string content = 1;
  string link = 2;
  string title = 3;
  string knowledge_repo_id = 4;
  string knowledge_repo_name = 5;
  float score = 6;
}

message KnowledgeRepoSearchResponse {
  repeated Knowledge knowledges = 1;
  string prompt = 2;
}

message ToolDetailRequest {
  int64 tool_id = 1;
  string tuid = 2;
  string username = 3;
}

message ToolParamsSchema {
  string body_params_schema = 1;
  string query_params_schema = 2;
  string header_params_schema = 3;
  string path_params_schema = 4;
}

message ToolAuthConfig {
  int32 auth_type = 1;
  string location = 2;
  string name = 3;
  string value = 4;
  string app_key = 5;
  string secret_key = 6;
}

message ToolDetailResponse {
  int64 id = 1;
  string tuid = 2;
  string name = 3;
  string icon = 4;
  string description = 5;
  string code = 6;
  string api_method = 7;
  string api_path = 8;
  ToolParamsSchema enter_params_schema = 9;
  string out_param_schema = 10;
  ToolAuthConfig auth_config = 11;
}

message ChatFile {
  string code = 1; // 代码内容
  string name = 2; // 文件名
  string language = 3; // 编程语言
}

message CodeSearchData {
  int64 id = 1;
  string path = 2;
  int64 start_line_no = 3;
  int64 end_line_no = 4;
  int64 start_col_no = 5;
  int64 end_col_no = 6;
  string code = 7;
  string language = 8;
  string function_name = 9;
  string function_signature = 10;
  string code_type = 11;
}

message CodeSearchRequest {
  string repo_name = 1; // 库名/仓库名
  string commit_id = 2; // 8位
  repeated string target_directory = 3; // 全仓库搜穿空[]，有指定路径的 llm-server/
  string query = 4;
  repeated ChatFile files = 5;
}

message CodeSearchResponse {
  repeated CodeSearchData list = 1;
  string prompt = 2;
  int32 total = 3;
}

service LlmServerService {
  rpc ListChatModels (ChatModelListQuery) returns (ChatModelListResponse) {}
  rpc ListAgents(AgentModelListQuery) returns (AgentModelListResponse) {}
  rpc KnowledgeRepoSearch(KnowledgeRepoSearchRequest) returns (KnowledgeRepoSearchResponse); //知识库检索
  rpc KnowledgeRepoSearchForDataAsset(KnowledgeRepoSearchRequest) returns (KnowledgeRepoSearchResponse); //知识库检索 - for 核心资产
  rpc GetToolDetail(ToolDetailRequest) returns (ToolDetailResponse);
  rpc CodeSearch (CodeSearchRequest) returns (CodeSearchResponse); //代码搜索
}