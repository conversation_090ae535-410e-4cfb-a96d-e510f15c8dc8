// GENERATED CODE -- DO NOT EDIT!

'use strict';
var grpc = require('@infra-node/grpc');
var llm_server_pb = require('./llm_server_pb.js');

function serialize_kwaipilot_AgentModelListQuery(arg) {
  if (!(arg instanceof llm_server_pb.AgentModelListQuery)) {
    throw new Error('Expected argument of type kwaipilot.AgentModelListQuery');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_AgentModelListQuery(buffer_arg) {
  return llm_server_pb.AgentModelListQuery.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_AgentModelListResponse(arg) {
  if (!(arg instanceof llm_server_pb.AgentModelListResponse)) {
    throw new Error('Expected argument of type kwaipilot.AgentModelListResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_AgentModelListResponse(buffer_arg) {
  return llm_server_pb.AgentModelListResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_ChatModelListQuery(arg) {
  if (!(arg instanceof llm_server_pb.ChatModelListQuery)) {
    throw new Error('Expected argument of type kwaipilot.ChatModelListQuery');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_ChatModelListQuery(buffer_arg) {
  return llm_server_pb.ChatModelListQuery.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_ChatModelListResponse(arg) {
  if (!(arg instanceof llm_server_pb.ChatModelListResponse)) {
    throw new Error('Expected argument of type kwaipilot.ChatModelListResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_ChatModelListResponse(buffer_arg) {
  return llm_server_pb.ChatModelListResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_CodeSearchRequest(arg) {
  if (!(arg instanceof llm_server_pb.CodeSearchRequest)) {
    throw new Error('Expected argument of type kwaipilot.CodeSearchRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_CodeSearchRequest(buffer_arg) {
  return llm_server_pb.CodeSearchRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_CodeSearchResponse(arg) {
  if (!(arg instanceof llm_server_pb.CodeSearchResponse)) {
    throw new Error('Expected argument of type kwaipilot.CodeSearchResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_CodeSearchResponse(buffer_arg) {
  return llm_server_pb.CodeSearchResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_KnowledgeRepoSearchRequest(arg) {
  if (!(arg instanceof llm_server_pb.KnowledgeRepoSearchRequest)) {
    throw new Error('Expected argument of type kwaipilot.KnowledgeRepoSearchRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_KnowledgeRepoSearchRequest(buffer_arg) {
  return llm_server_pb.KnowledgeRepoSearchRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_KnowledgeRepoSearchResponse(arg) {
  if (!(arg instanceof llm_server_pb.KnowledgeRepoSearchResponse)) {
    throw new Error('Expected argument of type kwaipilot.KnowledgeRepoSearchResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_KnowledgeRepoSearchResponse(buffer_arg) {
  return llm_server_pb.KnowledgeRepoSearchResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_ToolDetailRequest(arg) {
  if (!(arg instanceof llm_server_pb.ToolDetailRequest)) {
    throw new Error('Expected argument of type kwaipilot.ToolDetailRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_ToolDetailRequest(buffer_arg) {
  return llm_server_pb.ToolDetailRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_ToolDetailResponse(arg) {
  if (!(arg instanceof llm_server_pb.ToolDetailResponse)) {
    throw new Error('Expected argument of type kwaipilot.ToolDetailResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_ToolDetailResponse(buffer_arg) {
  return llm_server_pb.ToolDetailResponse.deserializeBinary(new Uint8Array(buffer_arg));
}


var LlmServerServiceService = exports.LlmServerServiceService = {
  listChatModels: {
    path: '/kwaipilot.LlmServerService/ListChatModels',
    requestStream: false,
    responseStream: false,
    requestType: llm_server_pb.ChatModelListQuery,
    responseType: llm_server_pb.ChatModelListResponse,
    requestSerialize: serialize_kwaipilot_ChatModelListQuery,
    requestDeserialize: deserialize_kwaipilot_ChatModelListQuery,
    responseSerialize: serialize_kwaipilot_ChatModelListResponse,
    responseDeserialize: deserialize_kwaipilot_ChatModelListResponse,
  },
  listAgents: {
    path: '/kwaipilot.LlmServerService/ListAgents',
    requestStream: false,
    responseStream: false,
    requestType: llm_server_pb.AgentModelListQuery,
    responseType: llm_server_pb.AgentModelListResponse,
    requestSerialize: serialize_kwaipilot_AgentModelListQuery,
    requestDeserialize: deserialize_kwaipilot_AgentModelListQuery,
    responseSerialize: serialize_kwaipilot_AgentModelListResponse,
    responseDeserialize: deserialize_kwaipilot_AgentModelListResponse,
  },
  knowledgeRepoSearch: {
    path: '/kwaipilot.LlmServerService/KnowledgeRepoSearch',
    requestStream: false,
    responseStream: false,
    requestType: llm_server_pb.KnowledgeRepoSearchRequest,
    responseType: llm_server_pb.KnowledgeRepoSearchResponse,
    requestSerialize: serialize_kwaipilot_KnowledgeRepoSearchRequest,
    requestDeserialize: deserialize_kwaipilot_KnowledgeRepoSearchRequest,
    responseSerialize: serialize_kwaipilot_KnowledgeRepoSearchResponse,
    responseDeserialize: deserialize_kwaipilot_KnowledgeRepoSearchResponse,
  },
  // 知识库检索
knowledgeRepoSearchForDataAsset: {
    path: '/kwaipilot.LlmServerService/KnowledgeRepoSearchForDataAsset',
    requestStream: false,
    responseStream: false,
    requestType: llm_server_pb.KnowledgeRepoSearchRequest,
    responseType: llm_server_pb.KnowledgeRepoSearchResponse,
    requestSerialize: serialize_kwaipilot_KnowledgeRepoSearchRequest,
    requestDeserialize: deserialize_kwaipilot_KnowledgeRepoSearchRequest,
    responseSerialize: serialize_kwaipilot_KnowledgeRepoSearchResponse,
    responseDeserialize: deserialize_kwaipilot_KnowledgeRepoSearchResponse,
  },
  // 知识库检索 - for 核心资产
getToolDetail: {
    path: '/kwaipilot.LlmServerService/GetToolDetail',
    requestStream: false,
    responseStream: false,
    requestType: llm_server_pb.ToolDetailRequest,
    responseType: llm_server_pb.ToolDetailResponse,
    requestSerialize: serialize_kwaipilot_ToolDetailRequest,
    requestDeserialize: deserialize_kwaipilot_ToolDetailRequest,
    responseSerialize: serialize_kwaipilot_ToolDetailResponse,
    responseDeserialize: deserialize_kwaipilot_ToolDetailResponse,
  },
  codeSearch: {
    path: '/kwaipilot.LlmServerService/CodeSearch',
    requestStream: false,
    responseStream: false,
    requestType: llm_server_pb.CodeSearchRequest,
    responseType: llm_server_pb.CodeSearchResponse,
    requestSerialize: serialize_kwaipilot_CodeSearchRequest,
    requestDeserialize: deserialize_kwaipilot_CodeSearchRequest,
    responseSerialize: serialize_kwaipilot_CodeSearchResponse,
    responseDeserialize: deserialize_kwaipilot_CodeSearchResponse,
  },
  // 代码搜索
};

exports.LlmServerServiceClient = grpc.makeGenericClientConstructor(LlmServerServiceService);
