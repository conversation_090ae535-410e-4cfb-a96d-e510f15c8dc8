import { getLLMModelConsumer as getLLMModelConsumer128K } from './128k';
import { getLLMModelConsumer as getLLMModelConsumer32K } from './32k';
import { getLLMModelConsumer as getLLMModelConsumer32KDataAsset } from './32k-data-asset';
import { getLLMModelConsumer as getLLMModelConsumer7B } from './7b';
import { getLLMModelConsumer as getLLMModelConsumer7BDataAsset } from './7b-data-asset';
import { createLogger } from '@fastgpt/global/common/util/logger';

import type { LLMCluster } from '@fastgpt/global/common/constants';

const logger = createLogger('get-llm-model-consumer');

export async function getLLMModelConsumer(model: string, cluster: LLMCluster) {
  logger.info(`model: ${model}, cluster: ${cluster}`);

  if (cluster === 'data-asset') {
    logger.info('use data-asset cluster');

    if (model === 'kwaipilot_pro_32k') {
      logger.info('use 32k data-asset cluster');
      return getLLMModelConsumer32KDataAsset();
    }
    if (model === 'kwaipilot_7b') {
      logger.info('use 7b data-asset cluster');
      return getLLMModelConsumer7BDataAsset();
    }
  } else {
    logger.info('use common cluster');

    if (model === 'kwaipilot_pro_32k') {
      logger.info('use 32k common cluster');
      return getLLMModelConsumer32K();
    }
    if (model === 'kwaipilot_turbo_128k') {
      logger.info('use 128k common cluster');
      return getLLMModelConsumer128K();
    }
    if (model === 'kwaipilot_7b') {
      logger.info('use 7b common cluster');
      return getLLMModelConsumer7B();
    }
  }

  throw new Error(`get llm consumer unknown model: ${model}, cluster: ${cluster}`);
}

export * from './proto/functioncall_kwaipilot_pb';
