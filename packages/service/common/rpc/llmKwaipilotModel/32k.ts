import { RPC, GRPCProtocol, KessRegistry, Consumer } from '@infra-node/rpc';
import { IConsumer } from './proto/functioncall_kwaipilot_grpc_pb';
import { IS_PREONLINE } from '@fastgpt/global/core/chat/constants';
import { TIMEOUT } from './const';

const GRPC_SERVICE = IS_PREONLINE
  ? 'grpc_python_kwaipilot_llm_api_sglang'
  : 'grpc_python_kwaipilot_llm_api';

const registry = new KessRegistry({
  balance: 'round-robin',
  serviceName: GRPC_SERVICE
});

// NOTE: 这里的参数设置很重要，不要修改，改完之后会出错
const protocol = new GRPCProtocol({
  methodLowerCamelCase: true,
  protoLoader: {
    enums: Number,
    oneofs: false
  },
  proto: require('./proto/functioncall_kwaipilot_grpc_pb')
});

let consumer: Consumer<IConsumer> | null = null;
export async function getLLMModelConsumer() {
  if (!consumer) {
    consumer = await RPC.createConsumer<IConsumer>({
      registry,
      protocol,
      timeout: TIMEOUT
    });
  }
  return consumer;
}

export * from './proto/functioncall_kwaipilot_pb';
