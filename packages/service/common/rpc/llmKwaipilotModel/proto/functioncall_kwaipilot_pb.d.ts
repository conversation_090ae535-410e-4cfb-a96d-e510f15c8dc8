// package: kwaipilot_llm_api
// file: functioncall_kwaipilot.proto

/* tslint:disable */
/* eslint-disable */

import * as jspb from 'google-protobuf';

export class KwaipilotLlmRequest extends jspb.Message {
  getRequestBodyJsonStr(): string;
  setRequestBodyJsonStr(value: string): KwaipilotLlmRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotLlmRequest.AsObject;
  static toObject(includeInstance: boolean, msg: KwaipilotLlmRequest): KwaipilotLlmRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: <PERSON>wai<PERSON>lotLlmRe<PERSON>, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotLlmRequest;
  static deserializeBinaryFromReader(
    message: Kwai<PERSON>lotLlmRequest,
    reader: jspb.BinaryReader
  ): KwaipilotLlmRequest;
}

export namespace KwaipilotLlmRequest {
  export type AsObject = {
    requestBodyJsonStr: string;
  };

  export type DynamicObject = {
    requestBodyJsonStr: string;
  };
}

export class KwaipilotLlmResponse extends jspb.Message {
  getResponseJsonStr(): string;
  setResponseJsonStr(value: string): KwaipilotLlmResponse;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KwaipilotLlmResponse.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KwaipilotLlmResponse
  ): KwaipilotLlmResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: KwaipilotLlmResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): KwaipilotLlmResponse;
  static deserializeBinaryFromReader(
    message: KwaipilotLlmResponse,
    reader: jspb.BinaryReader
  ): KwaipilotLlmResponse;
}

export namespace KwaipilotLlmResponse {
  export type AsObject = {
    responseJsonStr: string;
  };

  export type DynamicObject = {
    responseJsonStr: string;
  };
}
