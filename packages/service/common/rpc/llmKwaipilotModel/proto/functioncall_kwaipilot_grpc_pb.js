// GENERATED CODE -- DO NOT EDIT!

'use strict';
var grpc = require('@infra-node/grpc');
var functioncall_kwaipilot_pb = require('./functioncall_kwaipilot_pb.js');

function serialize_kwaipilot_llm_api_KwaipilotLlmRequest(arg) {
  if (!(arg instanceof functioncall_kwaipilot_pb.KwaipilotLlmRequest)) {
    throw new Error('Expected argument of type kwaipilot_llm_api.KwaipilotLlmRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_llm_api_KwaipilotLlmRequest(buffer_arg) {
  return functioncall_kwaipilot_pb.KwaipilotLlmRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_llm_api_KwaipilotLlmResponse(arg) {
  if (!(arg instanceof functioncall_kwaipilot_pb.KwaipilotLlmResponse)) {
    throw new Error('Expected argument of type kwaipilot_llm_api.KwaipilotLlmResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_llm_api_KwaipilotLlmResponse(buffer_arg) {
  return functioncall_kwaipilot_pb.KwaipilotLlmResponse.deserializeBinary(new Uint8Array(buffer_arg));
}


var KwaipilotLlmApiServiceService = exports.KwaipilotLlmApiServiceService = {
  llmChat: {
    path: '/kwaipilot_llm_api.KwaipilotLlmApiService/LlmChat',
    requestStream: false,
    responseStream: true,
    requestType: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    responseType: functioncall_kwaipilot_pb.KwaipilotLlmResponse,
    requestSerialize: serialize_kwaipilot_llm_api_KwaipilotLlmRequest,
    requestDeserialize: deserialize_kwaipilot_llm_api_KwaipilotLlmRequest,
    responseSerialize: serialize_kwaipilot_llm_api_KwaipilotLlmResponse,
    responseDeserialize: deserialize_kwaipilot_llm_api_KwaipilotLlmResponse,
  },
};

exports.KwaipilotLlmApiServiceClient = grpc.makeGenericClientConstructor(KwaipilotLlmApiServiceService);
