// package: kwaipilot_llm_api
// file: functioncall_kwaipilot.proto

/* tslint:disable */
/* eslint-disable */

import * as grpc from '@infra-node/grpc';
import * as functioncall_kwaipilot_pb from './functioncall_kwaipilot_pb';
import type { Metadata, RPCCallOptions } from '@infra-node/rpc';

export interface IKwaipilotLlmApiServiceClient {
  llmChat(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    metadata?: Metadata
  ): grpc.ClientReadableStream<functioncall_kwaipilot_pb.KwaipilotLlmResponse>;
  llmChat(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest,
    metadata?: Metadata,
    options?: RPCCallOptions
  ): grpc.ClientReadableStream<functioncall_kwaipilot_pb.KwaipilotLlmResponse>;
}

export interface IKwaipilotLlmApiServiceClientPromisify {}

export interface IKwaipilotLlmApiServiceClientDynamic {
  llmChat(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject,
    metadata?: Metadata
  ): grpc.ClientReadableStream<functioncall_kwaipilot_pb.KwaipilotLlmResponse.DynamicObject>;
  llmChat(
    request: functioncall_kwaipilot_pb.KwaipilotLlmRequest.DynamicObject,
    metadata?: Metadata,
    options?: RPCCallOptions
  ): grpc.ClientReadableStream<functioncall_kwaipilot_pb.KwaipilotLlmResponse.DynamicObject>;
}

export interface IKwaipilotLlmApiServiceClientDynamicPromisify {}

export interface IConsumer {
  KwaipilotLlmApiService: IKwaipilotLlmApiServiceClient;
  __promise__: {
    KwaipilotLlmApiService: IKwaipilotLlmApiServiceClientPromisify;
  };
}

export interface IConsumerDynamic {
  KwaipilotLlmApiService: IKwaipilotLlmApiServiceClientDynamic;
  __promise__: {
    KwaipilotLlmApiService: IKwaipilotLlmApiServiceClientDynamicPromisify;
  };
}
