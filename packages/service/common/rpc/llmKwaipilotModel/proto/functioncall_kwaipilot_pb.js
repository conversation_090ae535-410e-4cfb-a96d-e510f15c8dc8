// source: functioncall_kwaipilot.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotLlmRequest', null, global);
goog.exportSymbol('proto.kwaipilot_llm_api.KwaipilotLlmResponse', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotLlmRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotLlmRequest.displayName = 'proto.kwaipilot_llm_api.KwaipilotLlmRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot_llm_api.KwaipilotLlmResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot_llm_api.KwaipilotLlmResponse.displayName = 'proto.kwaipilot_llm_api.KwaipilotLlmResponse';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.KwaipilotLlmRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    requestBodyJsonStr: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmRequest}
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotLlmRequest;
  return proto.kwaipilot_llm_api.KwaipilotLlmRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmRequest}
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setRequestBodyJsonStr(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotLlmRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRequestBodyJsonStr();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string request_body_json_str = 1;
 * @return {string}
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.prototype.getRequestBodyJsonStr = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmRequest} returns this
 */
proto.kwaipilot_llm_api.KwaipilotLlmRequest.prototype.setRequestBodyJsonStr = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot_llm_api.KwaipilotLlmResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    responseJsonStr: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmResponse}
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot_llm_api.KwaipilotLlmResponse;
  return proto.kwaipilot_llm_api.KwaipilotLlmResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmResponse}
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setResponseJsonStr(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot_llm_api.KwaipilotLlmResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot_llm_api.KwaipilotLlmResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResponseJsonStr();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string response_json_str = 1;
 * @return {string}
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.prototype.getResponseJsonStr = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot_llm_api.KwaipilotLlmResponse} returns this
 */
proto.kwaipilot_llm_api.KwaipilotLlmResponse.prototype.setResponseJsonStr = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


goog.object.extend(exports, proto.kwaipilot_llm_api);
