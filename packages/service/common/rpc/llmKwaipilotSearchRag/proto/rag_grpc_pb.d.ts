// package: kwaipilot.search
// file: rag.proto

/* tslint:disable */
/* eslint-disable */

import * as grpc from "@infra-node/grpc";
import * as rag_pb from "./rag_pb";
import type { Metadata, RPCCallOptions } from '@infra-node/rpc';

export interface IKwaipilotSearchServiceClient {
    knowledgeRepoSearch(request: rag_pb.KnowledgeRepoSearchRequest, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoSearchResponse) => void): void;
    knowledgeRepoSearch(request: rag_pb.KnowledgeRepoSearchRequest, metadata: Metadata, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoSearchResponse) => void): void;
    knowledgeRepoSearch(request: rag_pb.KnowledgeRepoSearchRequest, metadata: Metadata, options: RPCCallOptions, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoSearchResponse) => void): void;
    codeSearch(request: rag_pb.CodeSearchRequest, callback: (error: grpc.ServiceError | null, response: rag_pb.CodeSearchResponse) => void): void;
    codeSearch(request: rag_pb.CodeSearchRequest, metadata: Metadata, callback: (error: grpc.ServiceError | null, response: rag_pb.CodeSearchResponse) => void): void;
    codeSearch(request: rag_pb.CodeSearchRequest, metadata: Metadata, options: RPCCallOptions, callback: (error: grpc.ServiceError | null, response: rag_pb.CodeSearchResponse) => void): void;
    knowledgeRepoStore(request: rag_pb.KnowledgeRepoStoreRequest, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoStoreResponse) => void): void;
    knowledgeRepoStore(request: rag_pb.KnowledgeRepoStoreRequest, metadata: Metadata, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoStoreResponse) => void): void;
    knowledgeRepoStore(request: rag_pb.KnowledgeRepoStoreRequest, metadata: Metadata, options: RPCCallOptions, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoStoreResponse) => void): void;
    knowledgeRepoDelete(request: rag_pb.KnowledgeRepoDeleteRequest, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoDeleteResponse) => void): void;
    knowledgeRepoDelete(request: rag_pb.KnowledgeRepoDeleteRequest, metadata: Metadata, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoDeleteResponse) => void): void;
    knowledgeRepoDelete(request: rag_pb.KnowledgeRepoDeleteRequest, metadata: Metadata, options: RPCCallOptions, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoDeleteResponse) => void): void;
    koncallQuerySearch(request: rag_pb.KOncallQuerySearchRequest, callback: (error: grpc.ServiceError | null, response: rag_pb.KOncallQuerySearchResponse) => void): void;
    koncallQuerySearch(request: rag_pb.KOncallQuerySearchRequest, metadata: Metadata, callback: (error: grpc.ServiceError | null, response: rag_pb.KOncallQuerySearchResponse) => void): void;
    koncallQuerySearch(request: rag_pb.KOncallQuerySearchRequest, metadata: Metadata, options: RPCCallOptions, callback: (error: grpc.ServiceError | null, response: rag_pb.KOncallQuerySearchResponse) => void): void;
    webSearch(request: rag_pb.WebSearchRequest, callback: (error: grpc.ServiceError | null, response: rag_pb.WebSearchResponse) => void): void;
    webSearch(request: rag_pb.WebSearchRequest, metadata: Metadata, callback: (error: grpc.ServiceError | null, response: rag_pb.WebSearchResponse) => void): void;
    webSearch(request: rag_pb.WebSearchRequest, metadata: Metadata, options: RPCCallOptions, callback: (error: grpc.ServiceError | null, response: rag_pb.WebSearchResponse) => void): void;
}

export interface IKwaipilotSearchServiceClientPromisify {
    knowledgeRepoSearch(request: rag_pb.KnowledgeRepoSearchRequest): Promise<rag_pb.KnowledgeRepoSearchResponse>;
    knowledgeRepoSearch(request: rag_pb.KnowledgeRepoSearchRequest, metadata: Metadata): Promise<rag_pb.KnowledgeRepoSearchResponse>;
    knowledgeRepoSearch(request: rag_pb.KnowledgeRepoSearchRequest, metadata: Metadata, options: RPCCallOptions): Promise<rag_pb.KnowledgeRepoSearchResponse>;
    codeSearch(request: rag_pb.CodeSearchRequest): Promise<rag_pb.CodeSearchResponse>;
    codeSearch(request: rag_pb.CodeSearchRequest, metadata: Metadata): Promise<rag_pb.CodeSearchResponse>;
    codeSearch(request: rag_pb.CodeSearchRequest, metadata: Metadata, options: RPCCallOptions): Promise<rag_pb.CodeSearchResponse>;
    knowledgeRepoStore(request: rag_pb.KnowledgeRepoStoreRequest): Promise<rag_pb.KnowledgeRepoStoreResponse>;
    knowledgeRepoStore(request: rag_pb.KnowledgeRepoStoreRequest, metadata: Metadata): Promise<rag_pb.KnowledgeRepoStoreResponse>;
    knowledgeRepoStore(request: rag_pb.KnowledgeRepoStoreRequest, metadata: Metadata, options: RPCCallOptions): Promise<rag_pb.KnowledgeRepoStoreResponse>;
    knowledgeRepoDelete(request: rag_pb.KnowledgeRepoDeleteRequest): Promise<rag_pb.KnowledgeRepoDeleteResponse>;
    knowledgeRepoDelete(request: rag_pb.KnowledgeRepoDeleteRequest, metadata: Metadata): Promise<rag_pb.KnowledgeRepoDeleteResponse>;
    knowledgeRepoDelete(request: rag_pb.KnowledgeRepoDeleteRequest, metadata: Metadata, options: RPCCallOptions): Promise<rag_pb.KnowledgeRepoDeleteResponse>;
    koncallQuerySearch(request: rag_pb.KOncallQuerySearchRequest): Promise<rag_pb.KOncallQuerySearchResponse>;
    koncallQuerySearch(request: rag_pb.KOncallQuerySearchRequest, metadata: Metadata): Promise<rag_pb.KOncallQuerySearchResponse>;
    koncallQuerySearch(request: rag_pb.KOncallQuerySearchRequest, metadata: Metadata, options: RPCCallOptions): Promise<rag_pb.KOncallQuerySearchResponse>;
    webSearch(request: rag_pb.WebSearchRequest): Promise<rag_pb.WebSearchResponse>;
    webSearch(request: rag_pb.WebSearchRequest, metadata: Metadata): Promise<rag_pb.WebSearchResponse>;
    webSearch(request: rag_pb.WebSearchRequest, metadata: Metadata, options: RPCCallOptions): Promise<rag_pb.WebSearchResponse>;
}


export interface IKwaipilotSearchServiceClientDynamic {
    knowledgeRepoSearch(request: rag_pb.KnowledgeRepoSearchRequest.DynamicObject, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoSearchResponse.DynamicObject) => void): void;
    knowledgeRepoSearch(request: rag_pb.KnowledgeRepoSearchRequest.DynamicObject, metadata: Metadata, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoSearchResponse.DynamicObject) => void): void;
    knowledgeRepoSearch(request: rag_pb.KnowledgeRepoSearchRequest.DynamicObject, metadata: Metadata, options: RPCCallOptions, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoSearchResponse.DynamicObject) => void): void;
    codeSearch(request: rag_pb.CodeSearchRequest.DynamicObject, callback: (error: grpc.ServiceError | null, response: rag_pb.CodeSearchResponse.DynamicObject) => void): void;
    codeSearch(request: rag_pb.CodeSearchRequest.DynamicObject, metadata: Metadata, callback: (error: grpc.ServiceError | null, response: rag_pb.CodeSearchResponse.DynamicObject) => void): void;
    codeSearch(request: rag_pb.CodeSearchRequest.DynamicObject, metadata: Metadata, options: RPCCallOptions, callback: (error: grpc.ServiceError | null, response: rag_pb.CodeSearchResponse.DynamicObject) => void): void;
    knowledgeRepoStore(request: rag_pb.KnowledgeRepoStoreRequest.DynamicObject, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoStoreResponse.DynamicObject) => void): void;
    knowledgeRepoStore(request: rag_pb.KnowledgeRepoStoreRequest.DynamicObject, metadata: Metadata, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoStoreResponse.DynamicObject) => void): void;
    knowledgeRepoStore(request: rag_pb.KnowledgeRepoStoreRequest.DynamicObject, metadata: Metadata, options: RPCCallOptions, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoStoreResponse.DynamicObject) => void): void;
    knowledgeRepoDelete(request: rag_pb.KnowledgeRepoDeleteRequest.DynamicObject, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoDeleteResponse.DynamicObject) => void): void;
    knowledgeRepoDelete(request: rag_pb.KnowledgeRepoDeleteRequest.DynamicObject, metadata: Metadata, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoDeleteResponse.DynamicObject) => void): void;
    knowledgeRepoDelete(request: rag_pb.KnowledgeRepoDeleteRequest.DynamicObject, metadata: Metadata, options: RPCCallOptions, callback: (error: grpc.ServiceError | null, response: rag_pb.KnowledgeRepoDeleteResponse.DynamicObject) => void): void;
    koncallQuerySearch(request: rag_pb.KOncallQuerySearchRequest.DynamicObject, callback: (error: grpc.ServiceError | null, response: rag_pb.KOncallQuerySearchResponse.DynamicObject) => void): void;
    koncallQuerySearch(request: rag_pb.KOncallQuerySearchRequest.DynamicObject, metadata: Metadata, callback: (error: grpc.ServiceError | null, response: rag_pb.KOncallQuerySearchResponse.DynamicObject) => void): void;
    koncallQuerySearch(request: rag_pb.KOncallQuerySearchRequest.DynamicObject, metadata: Metadata, options: RPCCallOptions, callback: (error: grpc.ServiceError | null, response: rag_pb.KOncallQuerySearchResponse.DynamicObject) => void): void;
    webSearch(request: rag_pb.WebSearchRequest.DynamicObject, callback: (error: grpc.ServiceError | null, response: rag_pb.WebSearchResponse.DynamicObject) => void): void;
    webSearch(request: rag_pb.WebSearchRequest.DynamicObject, metadata: Metadata, callback: (error: grpc.ServiceError | null, response: rag_pb.WebSearchResponse.DynamicObject) => void): void;
    webSearch(request: rag_pb.WebSearchRequest.DynamicObject, metadata: Metadata, options: RPCCallOptions, callback: (error: grpc.ServiceError | null, response: rag_pb.WebSearchResponse.DynamicObject) => void): void;
}

export interface IKwaipilotSearchServiceClientDynamicPromisify {
    knowledgeRepoSearch(request: rag_pb.KnowledgeRepoSearchRequest.DynamicObject): Promise<rag_pb.KnowledgeRepoSearchResponse.DynamicObject>;
    knowledgeRepoSearch(request: rag_pb.KnowledgeRepoSearchRequest.DynamicObject, metadata: Metadata): Promise<rag_pb.KnowledgeRepoSearchResponse.DynamicObject>;
    knowledgeRepoSearch(request: rag_pb.KnowledgeRepoSearchRequest.DynamicObject, metadata: Metadata, options: RPCCallOptions): Promise<rag_pb.KnowledgeRepoSearchResponse.DynamicObject>;
    codeSearch(request: rag_pb.CodeSearchRequest.DynamicObject): Promise<rag_pb.CodeSearchResponse.DynamicObject>;
    codeSearch(request: rag_pb.CodeSearchRequest.DynamicObject, metadata: Metadata): Promise<rag_pb.CodeSearchResponse.DynamicObject>;
    codeSearch(request: rag_pb.CodeSearchRequest.DynamicObject, metadata: Metadata, options: RPCCallOptions): Promise<rag_pb.CodeSearchResponse.DynamicObject>;
    knowledgeRepoStore(request: rag_pb.KnowledgeRepoStoreRequest.DynamicObject): Promise<rag_pb.KnowledgeRepoStoreResponse.DynamicObject>;
    knowledgeRepoStore(request: rag_pb.KnowledgeRepoStoreRequest.DynamicObject, metadata: Metadata): Promise<rag_pb.KnowledgeRepoStoreResponse.DynamicObject>;
    knowledgeRepoStore(request: rag_pb.KnowledgeRepoStoreRequest.DynamicObject, metadata: Metadata, options: RPCCallOptions): Promise<rag_pb.KnowledgeRepoStoreResponse.DynamicObject>;
    knowledgeRepoDelete(request: rag_pb.KnowledgeRepoDeleteRequest.DynamicObject): Promise<rag_pb.KnowledgeRepoDeleteResponse.DynamicObject>;
    knowledgeRepoDelete(request: rag_pb.KnowledgeRepoDeleteRequest.DynamicObject, metadata: Metadata): Promise<rag_pb.KnowledgeRepoDeleteResponse.DynamicObject>;
    knowledgeRepoDelete(request: rag_pb.KnowledgeRepoDeleteRequest.DynamicObject, metadata: Metadata, options: RPCCallOptions): Promise<rag_pb.KnowledgeRepoDeleteResponse.DynamicObject>;
    koncallQuerySearch(request: rag_pb.KOncallQuerySearchRequest.DynamicObject): Promise<rag_pb.KOncallQuerySearchResponse.DynamicObject>;
    koncallQuerySearch(request: rag_pb.KOncallQuerySearchRequest.DynamicObject, metadata: Metadata): Promise<rag_pb.KOncallQuerySearchResponse.DynamicObject>;
    koncallQuerySearch(request: rag_pb.KOncallQuerySearchRequest.DynamicObject, metadata: Metadata, options: RPCCallOptions): Promise<rag_pb.KOncallQuerySearchResponse.DynamicObject>;
    webSearch(request: rag_pb.WebSearchRequest.DynamicObject): Promise<rag_pb.WebSearchResponse.DynamicObject>;
    webSearch(request: rag_pb.WebSearchRequest.DynamicObject, metadata: Metadata): Promise<rag_pb.WebSearchResponse.DynamicObject>;
    webSearch(request: rag_pb.WebSearchRequest.DynamicObject, metadata: Metadata, options: RPCCallOptions): Promise<rag_pb.WebSearchResponse.DynamicObject>;
}


export interface IConsumer {
    KwaipilotSearchService: IKwaipilotSearchServiceClient;
    __promise__: {
        KwaipilotSearchService: IKwaipilotSearchServiceClientPromisify;
    };
}

export interface IConsumerDynamic {
    KwaipilotSearchService: IKwaipilotSearchServiceClientDynamic;
    __promise__: {
        KwaipilotSearchService: IKwaipilotSearchServiceClientDynamicPromisify;
    };
}


