syntax = "proto3";

option java_package = "com.kuaishou.kwaipilot.rpc.search";
option java_outer_classname = "KwaipilotSearchServiceProto";
option java_multiple_files = true;

package kwaipilot.search;

service KwaipilotSearchService {
  rpc KnowledgeRepoSearch(KnowledgeRepoSearchRequest) returns (KnowledgeRepoSearchResponse); //知识库检索
  rpc CodeSearch (CodeSearchRequest) returns (CodeSearchResponse); //代码搜索
  rpc KnowledgeRepoStore(KnowledgeRepoStoreRequest) returns (KnowledgeRepoStoreResponse); //知识库存储，超时
  rpc KnowledgeRepoDelete(KnowledgeRepoDeleteRequest) returns (KnowledgeRepoDeleteResponse); // 知识库删除文档
  rpc KoncallQuerySearch(KOncallQuerySearchRequest) returns (KOncallQuerySearchResponse);
  rpc WebSearch(WebSearchRequest) returns (WebSearchResponse);
}

message OperationResult{
  string link = 1;
  string result = 2; // success - 成功；fail - 失败
}

message KnowledgeRepoDeleteRequest{
  string knowledge_repo_id = 1;
  string knowledge_repo_name = 2;
  repeated string links = 3;
}

message KnowledgeRepoDeleteResponse{
  repeated OperationResult results = 1;
}

message Doc {
  string name = 1; // 文档名
  string link = 2; // 链接
  string source_type = 3; // 类型，doc - doc文档；sheet - doc表格；md_content - markdown格式的文本内容，会直接读取content字段的内容
  int32 status = 4; // 状态，1
  string content = 5;
  int64 id = 6; // knowledge_id
}

message KnowledgeRepoStoreRequest {
  repeated Doc docs = 1;
  string knowledge_repo_id = 2;
  string knowledge_repo_name = 3;
  string request_id = 4;
}

message KnowledgeRepoStoreResponse {
  string status = 1; // success - 构建成功；fail - 构建失败
}

message KnowledgeRepoSearchRequest {
  string platform = 1; // 请求来源平台，KwaipilotAgent
  repeated string knowledge_repo_ids = 2;
  string query = 3;
  int32 top_k = 4;
  repeated ChatMessage chat_history = 5;
}

message KOncallQuerySearchRequest {
  string query_id = 1;
  string query = 2;
  string collection = 3;
  int32 retrieve_cnt = 4;
  int32 rerank_cnt = 5;
  float dense_ratio = 6;
  float threshold = 7;
}

message KOncallQuerySearchResponse {
  repeated SimilarQueries similar_queries = 1;
}

message SimilarQueries {
  string query_id = 1;
  string query = 2;
  float score = 3;
}

message ChatMessage {
  string role = 1;
  string content = 2;
}

message Knowledge {
  string content = 1;
  string link = 2;
  string title = 3;
  string knowledge_repo_id = 4;
  string knowledge_repo_name = 5;
  float score = 6;
}

message KnowledgeRepoSearchResponse {
  repeated Knowledge knowledges = 1;
  string prompt = 2;
}

message ChatFile {
  string code = 1; // 代码内容
  string name = 2; // 文件名
  string language = 3; // 编程语言
}

message CodeSearchRequest {
  string repo_name = 1; // 库名/仓库名
  string commit_id = 2; // 8位
  repeated string target_directory = 3; // 全仓库搜穿空[]，有指定路径的 llm-server/
  string query = 4;
  repeated ChatFile files = 5;
}

message CodeSearchResult {
  string code_content = 1;
  float score = 2;
  string metadata = 3; // str of json
}

message CodeSearchResponse {
  repeated CodeSearchResult code_search_results = 1;
  string prompt = 2;
}

message UploadedFile{
  string filename = 1; // 文件名
  int32 file_index = 2; //第几个文件
  int32 page_num = 3; // pdf的页码，其他文件类型传-1
  string page_content = 4; // 内容
}

message WebSearchRequest{
  string query = 1;
  repeated UploadedFile uploaded_files = 2;
}

message WebSearchResponse {
  repeated WebSnippet web_snippets = 1;
}

message WebSnippet {
  string title = 1;
  string link = 2;
  string date = 3;
  string favicon = 4;
  string content = 5;
  string prev_content = 6;
  string next_content = 7;
  int32 file_index = 8; //文档的话是第几个文件
  int32 page_num = 9; // pdf的页码，其他文件类型传-1
}