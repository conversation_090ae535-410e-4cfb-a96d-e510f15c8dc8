// package: kwaipilot.search
// file: rag.proto

/* tslint:disable */
/* eslint-disable */

import * as jspb from 'google-protobuf';

export class OperationResult extends jspb.Message {
  getLink(): string;
  setLink(value: string): OperationResult;
  getResult(): string;
  setResult(value: string): OperationResult;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): OperationResult.AsObject;
  static toObject(includeInstance: boolean, msg: OperationResult): OperationResult.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: OperationResult, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): OperationResult;
  static deserializeBinaryFromReader(
    message: OperationR<PERSON><PERSON>,
    reader: jspb.BinaryReader
  ): OperationResult;
}

export namespace OperationResult {
  export type AsObject = {
    link: string;
    result: string;
  };

  export type DynamicObject = {
    link: string;
    result: string;
  };
}

export class KnowledgeRepoDeleteRequest extends jspb.Message {
  getKnowledgeRepoId(): string;
  setKnowledgeRepoId(value: string): KnowledgeRepoDeleteRequest;
  getKnowledgeRepoName(): string;
  setKnowledgeRepoName(value: string): KnowledgeRepoDeleteRequest;
  clearLinksList(): void;
  getLinksList(): Array<string>;
  setLinksList(value: Array<string>): KnowledgeRepoDeleteRequest;
  addLinks(value: string, index?: number): string;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KnowledgeRepoDeleteRequest.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KnowledgeRepoDeleteRequest
  ): KnowledgeRepoDeleteRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KnowledgeRepoDeleteRequest,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KnowledgeRepoDeleteRequest;
  static deserializeBinaryFromReader(
    message: KnowledgeRepoDeleteRequest,
    reader: jspb.BinaryReader
  ): KnowledgeRepoDeleteRequest;
}

export namespace KnowledgeRepoDeleteRequest {
  export type AsObject = {
    knowledgeRepoId: string;
    knowledgeRepoName: string;
    linksList: Array<string>;
  };

  export type DynamicObject = {
    knowledgeRepoId: string;
    knowledgeRepoName: string;
    links: Array<string>;
  };
}

export class KnowledgeRepoDeleteResponse extends jspb.Message {
  clearResultsList(): void;
  getResultsList(): Array<OperationResult>;
  setResultsList(value: Array<OperationResult>): KnowledgeRepoDeleteResponse;
  addResults(value?: OperationResult, index?: number): OperationResult;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KnowledgeRepoDeleteResponse.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KnowledgeRepoDeleteResponse
  ): KnowledgeRepoDeleteResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KnowledgeRepoDeleteResponse,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KnowledgeRepoDeleteResponse;
  static deserializeBinaryFromReader(
    message: KnowledgeRepoDeleteResponse,
    reader: jspb.BinaryReader
  ): KnowledgeRepoDeleteResponse;
}

export namespace KnowledgeRepoDeleteResponse {
  export type AsObject = {
    resultsList: Array<OperationResult.AsObject>;
  };

  export type DynamicObject = {
    results: Array<OperationResult.DynamicObject>;
  };
}

export class Doc extends jspb.Message {
  getName(): string;
  setName(value: string): Doc;
  getLink(): string;
  setLink(value: string): Doc;
  getSourceType(): string;
  setSourceType(value: string): Doc;
  getStatus(): number;
  setStatus(value: number): Doc;
  getContent(): string;
  setContent(value: string): Doc;
  getId(): number;
  setId(value: number): Doc;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Doc.AsObject;
  static toObject(includeInstance: boolean, msg: Doc): Doc.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: Doc, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Doc;
  static deserializeBinaryFromReader(message: Doc, reader: jspb.BinaryReader): Doc;
}

export namespace Doc {
  export type AsObject = {
    name: string;
    link: string;
    sourceType: string;
    status: number;
    content: string;
    id: number;
  };

  export type DynamicObject = {
    name: string;
    link: string;
    sourceType: string;
    status: number;
    content: string;
    id: string;
  };
}

export class KnowledgeRepoStoreRequest extends jspb.Message {
  clearDocsList(): void;
  getDocsList(): Array<Doc>;
  setDocsList(value: Array<Doc>): KnowledgeRepoStoreRequest;
  addDocs(value?: Doc, index?: number): Doc;
  getKnowledgeRepoId(): string;
  setKnowledgeRepoId(value: string): KnowledgeRepoStoreRequest;
  getKnowledgeRepoName(): string;
  setKnowledgeRepoName(value: string): KnowledgeRepoStoreRequest;
  getRequestId(): string;
  setRequestId(value: string): KnowledgeRepoStoreRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KnowledgeRepoStoreRequest.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KnowledgeRepoStoreRequest
  ): KnowledgeRepoStoreRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KnowledgeRepoStoreRequest,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KnowledgeRepoStoreRequest;
  static deserializeBinaryFromReader(
    message: KnowledgeRepoStoreRequest,
    reader: jspb.BinaryReader
  ): KnowledgeRepoStoreRequest;
}

export namespace KnowledgeRepoStoreRequest {
  export type AsObject = {
    docsList: Array<Doc.AsObject>;
    knowledgeRepoId: string;
    knowledgeRepoName: string;
    requestId: string;
  };

  export type DynamicObject = {
    docs: Array<Doc.DynamicObject>;
    knowledgeRepoId: string;
    knowledgeRepoName: string;
    requestId: string;
  };
}

export class KnowledgeRepoStoreResponse extends jspb.Message {
  getStatus(): string;
  setStatus(value: string): KnowledgeRepoStoreResponse;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KnowledgeRepoStoreResponse.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KnowledgeRepoStoreResponse
  ): KnowledgeRepoStoreResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KnowledgeRepoStoreResponse,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KnowledgeRepoStoreResponse;
  static deserializeBinaryFromReader(
    message: KnowledgeRepoStoreResponse,
    reader: jspb.BinaryReader
  ): KnowledgeRepoStoreResponse;
}

export namespace KnowledgeRepoStoreResponse {
  export type AsObject = {
    status: string;
  };

  export type DynamicObject = {
    status: string;
  };
}

export class KnowledgeRepoSearchRequest extends jspb.Message {
  getPlatform(): string;
  setPlatform(value: string): KnowledgeRepoSearchRequest;
  clearKnowledgeRepoIdsList(): void;
  getKnowledgeRepoIdsList(): Array<string>;
  setKnowledgeRepoIdsList(value: Array<string>): KnowledgeRepoSearchRequest;
  addKnowledgeRepoIds(value: string, index?: number): string;
  getQuery(): string;
  setQuery(value: string): KnowledgeRepoSearchRequest;
  getTopK(): number;
  setTopK(value: number): KnowledgeRepoSearchRequest;
  clearChatHistoryList(): void;
  getChatHistoryList(): Array<ChatMessage>;
  setChatHistoryList(value: Array<ChatMessage>): KnowledgeRepoSearchRequest;
  addChatHistory(value?: ChatMessage, index?: number): ChatMessage;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KnowledgeRepoSearchRequest.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KnowledgeRepoSearchRequest
  ): KnowledgeRepoSearchRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KnowledgeRepoSearchRequest,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KnowledgeRepoSearchRequest;
  static deserializeBinaryFromReader(
    message: KnowledgeRepoSearchRequest,
    reader: jspb.BinaryReader
  ): KnowledgeRepoSearchRequest;
}

export namespace KnowledgeRepoSearchRequest {
  export type AsObject = {
    platform: string;
    knowledgeRepoIdsList: Array<string>;
    query: string;
    topK: number;
    chatHistoryList: Array<ChatMessage.AsObject>;
  };

  export type DynamicObject = {
    platform: string;
    knowledgeRepoIds: Array<string>;
    query: string;
    topK: number;
    chatHistory: Array<ChatMessage.DynamicObject>;
  };
}

export class KOncallQuerySearchRequest extends jspb.Message {
  getQueryId(): string;
  setQueryId(value: string): KOncallQuerySearchRequest;
  getQuery(): string;
  setQuery(value: string): KOncallQuerySearchRequest;
  getCollection(): string;
  setCollection(value: string): KOncallQuerySearchRequest;
  getRetrieveCnt(): number;
  setRetrieveCnt(value: number): KOncallQuerySearchRequest;
  getRerankCnt(): number;
  setRerankCnt(value: number): KOncallQuerySearchRequest;
  getDenseRatio(): number;
  setDenseRatio(value: number): KOncallQuerySearchRequest;
  getThreshold(): number;
  setThreshold(value: number): KOncallQuerySearchRequest;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KOncallQuerySearchRequest.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KOncallQuerySearchRequest
  ): KOncallQuerySearchRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KOncallQuerySearchRequest,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KOncallQuerySearchRequest;
  static deserializeBinaryFromReader(
    message: KOncallQuerySearchRequest,
    reader: jspb.BinaryReader
  ): KOncallQuerySearchRequest;
}

export namespace KOncallQuerySearchRequest {
  export type AsObject = {
    queryId: string;
    query: string;
    collection: string;
    retrieveCnt: number;
    rerankCnt: number;
    denseRatio: number;
    threshold: number;
  };

  export type DynamicObject = {
    queryId: string;
    query: string;
    collection: string;
    retrieveCnt: number;
    rerankCnt: number;
    denseRatio: number;
    threshold: number;
  };
}

export class KOncallQuerySearchResponse extends jspb.Message {
  clearSimilarQueriesList(): void;
  getSimilarQueriesList(): Array<SimilarQueries>;
  setSimilarQueriesList(value: Array<SimilarQueries>): KOncallQuerySearchResponse;
  addSimilarQueries(value?: SimilarQueries, index?: number): SimilarQueries;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KOncallQuerySearchResponse.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KOncallQuerySearchResponse
  ): KOncallQuerySearchResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KOncallQuerySearchResponse,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KOncallQuerySearchResponse;
  static deserializeBinaryFromReader(
    message: KOncallQuerySearchResponse,
    reader: jspb.BinaryReader
  ): KOncallQuerySearchResponse;
}

export namespace KOncallQuerySearchResponse {
  export type AsObject = {
    similarQueriesList: Array<SimilarQueries.AsObject>;
  };

  export type DynamicObject = {
    similarQueries: Array<SimilarQueries.DynamicObject>;
  };
}

export class SimilarQueries extends jspb.Message {
  getQueryId(): string;
  setQueryId(value: string): SimilarQueries;
  getQuery(): string;
  setQuery(value: string): SimilarQueries;
  getScore(): number;
  setScore(value: number): SimilarQueries;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): SimilarQueries.AsObject;
  static toObject(includeInstance: boolean, msg: SimilarQueries): SimilarQueries.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: SimilarQueries, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): SimilarQueries;
  static deserializeBinaryFromReader(
    message: SimilarQueries,
    reader: jspb.BinaryReader
  ): SimilarQueries;
}

export namespace SimilarQueries {
  export type AsObject = {
    queryId: string;
    query: string;
    score: number;
  };

  export type DynamicObject = {
    queryId: string;
    query: string;
    score: number;
  };
}

export class ChatMessage extends jspb.Message {
  getRole(): string;
  setRole(value: string): ChatMessage;
  getContent(): string;
  setContent(value: string): ChatMessage;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ChatMessage.AsObject;
  static toObject(includeInstance: boolean, msg: ChatMessage): ChatMessage.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: ChatMessage, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ChatMessage;
  static deserializeBinaryFromReader(message: ChatMessage, reader: jspb.BinaryReader): ChatMessage;
}

export namespace ChatMessage {
  export type AsObject = {
    role: string;
    content: string;
  };

  export type DynamicObject = {
    role: string;
    content: string;
  };
}

export class Knowledge extends jspb.Message {
  getContent(): string;
  setContent(value: string): Knowledge;
  getLink(): string;
  setLink(value: string): Knowledge;
  getTitle(): string;
  setTitle(value: string): Knowledge;
  getKnowledgeRepoId(): string;
  setKnowledgeRepoId(value: string): Knowledge;
  getKnowledgeRepoName(): string;
  setKnowledgeRepoName(value: string): Knowledge;
  getScore(): number;
  setScore(value: number): Knowledge;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): Knowledge.AsObject;
  static toObject(includeInstance: boolean, msg: Knowledge): Knowledge.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: Knowledge, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): Knowledge;
  static deserializeBinaryFromReader(message: Knowledge, reader: jspb.BinaryReader): Knowledge;
}

export namespace Knowledge {
  export type AsObject = {
    content: string;
    link: string;
    title: string;
    knowledgeRepoId: string;
    knowledgeRepoName: string;
    score: number;
  };

  export type DynamicObject = {
    content: string;
    link: string;
    title: string;
    knowledgeRepoId: string;
    knowledgeRepoName: string;
    score: number;
  };
}

export class KnowledgeRepoSearchResponse extends jspb.Message {
  clearKnowledgesList(): void;
  getKnowledgesList(): Array<Knowledge>;
  setKnowledgesList(value: Array<Knowledge>): KnowledgeRepoSearchResponse;
  addKnowledges(value?: Knowledge, index?: number): Knowledge;
  getPrompt(): string;
  setPrompt(value: string): KnowledgeRepoSearchResponse;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): KnowledgeRepoSearchResponse.AsObject;
  static toObject(
    includeInstance: boolean,
    msg: KnowledgeRepoSearchResponse
  ): KnowledgeRepoSearchResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(
    message: KnowledgeRepoSearchResponse,
    writer: jspb.BinaryWriter
  ): void;
  static deserializeBinary(bytes: Uint8Array): KnowledgeRepoSearchResponse;
  static deserializeBinaryFromReader(
    message: KnowledgeRepoSearchResponse,
    reader: jspb.BinaryReader
  ): KnowledgeRepoSearchResponse;
}

export namespace KnowledgeRepoSearchResponse {
  export type AsObject = {
    knowledgesList: Array<Knowledge.AsObject>;
    prompt: string;
  };

  export type DynamicObject = {
    knowledges: Array<Knowledge.DynamicObject>;
    prompt: string;
  };
}

export class ChatFile extends jspb.Message {
  getCode(): string;
  setCode(value: string): ChatFile;
  getName(): string;
  setName(value: string): ChatFile;
  getLanguage(): string;
  setLanguage(value: string): ChatFile;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): ChatFile.AsObject;
  static toObject(includeInstance: boolean, msg: ChatFile): ChatFile.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: ChatFile, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): ChatFile;
  static deserializeBinaryFromReader(message: ChatFile, reader: jspb.BinaryReader): ChatFile;
}

export namespace ChatFile {
  export type AsObject = {
    code: string;
    name: string;
    language: string;
  };

  export type DynamicObject = {
    code: string;
    name: string;
    language: string;
  };
}

export class CodeSearchRequest extends jspb.Message {
  getRepoName(): string;
  setRepoName(value: string): CodeSearchRequest;
  getCommitId(): string;
  setCommitId(value: string): CodeSearchRequest;
  clearTargetDirectoryList(): void;
  getTargetDirectoryList(): Array<string>;
  setTargetDirectoryList(value: Array<string>): CodeSearchRequest;
  addTargetDirectory(value: string, index?: number): string;
  getQuery(): string;
  setQuery(value: string): CodeSearchRequest;
  clearFilesList(): void;
  getFilesList(): Array<ChatFile>;
  setFilesList(value: Array<ChatFile>): CodeSearchRequest;
  addFiles(value?: ChatFile, index?: number): ChatFile;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CodeSearchRequest.AsObject;
  static toObject(includeInstance: boolean, msg: CodeSearchRequest): CodeSearchRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: CodeSearchRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CodeSearchRequest;
  static deserializeBinaryFromReader(
    message: CodeSearchRequest,
    reader: jspb.BinaryReader
  ): CodeSearchRequest;
}

export namespace CodeSearchRequest {
  export type AsObject = {
    repoName: string;
    commitId: string;
    targetDirectoryList: Array<string>;
    query: string;
    filesList: Array<ChatFile.AsObject>;
  };

  export type DynamicObject = {
    repoName: string;
    commitId: string;
    targetDirectory: Array<string>;
    query: string;
    files: Array<ChatFile.DynamicObject>;
  };
}

export class CodeSearchResult extends jspb.Message {
  getCodeContent(): string;
  setCodeContent(value: string): CodeSearchResult;
  getScore(): number;
  setScore(value: number): CodeSearchResult;
  getMetadata(): string;
  setMetadata(value: string): CodeSearchResult;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CodeSearchResult.AsObject;
  static toObject(includeInstance: boolean, msg: CodeSearchResult): CodeSearchResult.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: CodeSearchResult, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CodeSearchResult;
  static deserializeBinaryFromReader(
    message: CodeSearchResult,
    reader: jspb.BinaryReader
  ): CodeSearchResult;
}

export namespace CodeSearchResult {
  export type AsObject = {
    codeContent: string;
    score: number;
    metadata: string;
  };

  export type DynamicObject = {
    codeContent: string;
    score: number;
    metadata: string;
  };
}

export class CodeSearchResponse extends jspb.Message {
  clearCodeSearchResultsList(): void;
  getCodeSearchResultsList(): Array<CodeSearchResult>;
  setCodeSearchResultsList(value: Array<CodeSearchResult>): CodeSearchResponse;
  addCodeSearchResults(value?: CodeSearchResult, index?: number): CodeSearchResult;
  getPrompt(): string;
  setPrompt(value: string): CodeSearchResponse;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): CodeSearchResponse.AsObject;
  static toObject(includeInstance: boolean, msg: CodeSearchResponse): CodeSearchResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: CodeSearchResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): CodeSearchResponse;
  static deserializeBinaryFromReader(
    message: CodeSearchResponse,
    reader: jspb.BinaryReader
  ): CodeSearchResponse;
}

export namespace CodeSearchResponse {
  export type AsObject = {
    codeSearchResultsList: Array<CodeSearchResult.AsObject>;
    prompt: string;
  };

  export type DynamicObject = {
    codeSearchResults: Array<CodeSearchResult.DynamicObject>;
    prompt: string;
  };
}

export class UploadedFile extends jspb.Message {
  getFilename(): string;
  setFilename(value: string): UploadedFile;
  getFileIndex(): number;
  setFileIndex(value: number): UploadedFile;
  getPageNum(): number;
  setPageNum(value: number): UploadedFile;
  getPageContent(): string;
  setPageContent(value: string): UploadedFile;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): UploadedFile.AsObject;
  static toObject(includeInstance: boolean, msg: UploadedFile): UploadedFile.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: UploadedFile, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): UploadedFile;
  static deserializeBinaryFromReader(
    message: UploadedFile,
    reader: jspb.BinaryReader
  ): UploadedFile;
}

export namespace UploadedFile {
  export type AsObject = {
    filename: string;
    fileIndex: number;
    pageNum: number;
    pageContent: string;
  };

  export type DynamicObject = {
    filename: string;
    fileIndex: number;
    pageNum: number;
    pageContent: string;
  };
}

export class WebSearchRequest extends jspb.Message {
  getQuery(): string;
  setQuery(value: string): WebSearchRequest;
  clearUploadedFilesList(): void;
  getUploadedFilesList(): Array<UploadedFile>;
  setUploadedFilesList(value: Array<UploadedFile>): WebSearchRequest;
  addUploadedFiles(value?: UploadedFile, index?: number): UploadedFile;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): WebSearchRequest.AsObject;
  static toObject(includeInstance: boolean, msg: WebSearchRequest): WebSearchRequest.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: WebSearchRequest, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): WebSearchRequest;
  static deserializeBinaryFromReader(
    message: WebSearchRequest,
    reader: jspb.BinaryReader
  ): WebSearchRequest;
}

export namespace WebSearchRequest {
  export type AsObject = {
    query: string;
    uploadedFilesList: Array<UploadedFile.AsObject>;
  };

  export type DynamicObject = {
    query: string;
    uploadedFiles: Array<UploadedFile.DynamicObject>;
  };
}

export class WebSearchResponse extends jspb.Message {
  clearWebSnippetsList(): void;
  getWebSnippetsList(): Array<WebSnippet>;
  setWebSnippetsList(value: Array<WebSnippet>): WebSearchResponse;
  addWebSnippets(value?: WebSnippet, index?: number): WebSnippet;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): WebSearchResponse.AsObject;
  static toObject(includeInstance: boolean, msg: WebSearchResponse): WebSearchResponse.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: WebSearchResponse, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): WebSearchResponse;
  static deserializeBinaryFromReader(
    message: WebSearchResponse,
    reader: jspb.BinaryReader
  ): WebSearchResponse;
}

export namespace WebSearchResponse {
  export type AsObject = {
    webSnippetsList: Array<WebSnippet.AsObject>;
  };

  export type DynamicObject = {
    webSnippets: Array<WebSnippet.DynamicObject>;
  };
}

export class WebSnippet extends jspb.Message {
  getTitle(): string;
  setTitle(value: string): WebSnippet;
  getLink(): string;
  setLink(value: string): WebSnippet;
  getDate(): string;
  setDate(value: string): WebSnippet;
  getFavicon(): string;
  setFavicon(value: string): WebSnippet;
  getContent(): string;
  setContent(value: string): WebSnippet;
  getPrevContent(): string;
  setPrevContent(value: string): WebSnippet;
  getNextContent(): string;
  setNextContent(value: string): WebSnippet;
  getFileIndex(): number;
  setFileIndex(value: number): WebSnippet;
  getPageNum(): number;
  setPageNum(value: number): WebSnippet;

  serializeBinary(): Uint8Array;
  toObject(includeInstance?: boolean): WebSnippet.AsObject;
  static toObject(includeInstance: boolean, msg: WebSnippet): WebSnippet.AsObject;
  static extensions: { [key: number]: jspb.ExtensionFieldInfo<jspb.Message> };
  static extensionsBinary: { [key: number]: jspb.ExtensionFieldBinaryInfo<jspb.Message> };
  static serializeBinaryToWriter(message: WebSnippet, writer: jspb.BinaryWriter): void;
  static deserializeBinary(bytes: Uint8Array): WebSnippet;
  static deserializeBinaryFromReader(message: WebSnippet, reader: jspb.BinaryReader): WebSnippet;
}

export namespace WebSnippet {
  export type AsObject = {
    title: string;
    link: string;
    date: string;
    favicon: string;
    content: string;
    prevContent: string;
    nextContent: string;
    fileIndex: number;
    pageNum: number;
  };

  export type DynamicObject = {
    title: string;
    link: string;
    date: string;
    favicon: string;
    content: string;
    prevContent: string;
    nextContent: string;
    fileIndex: number;
    pageNum: number;
  };
}
