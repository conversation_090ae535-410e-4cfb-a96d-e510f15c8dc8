// GENERATED CODE -- DO NOT EDIT!

'use strict';
var grpc = require('@infra-node/grpc');
var rag_pb = require('./rag_pb.js');

function serialize_kwaipilot_search_CodeSearchRequest(arg) {
  if (!(arg instanceof rag_pb.CodeSearchRequest)) {
    throw new Error('Expected argument of type kwaipilot.search.CodeSearchRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_search_CodeSearchRequest(buffer_arg) {
  return rag_pb.CodeSearchRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_search_CodeSearchResponse(arg) {
  if (!(arg instanceof rag_pb.CodeSearchResponse)) {
    throw new Error('Expected argument of type kwaipilot.search.CodeSearchResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_search_CodeSearchResponse(buffer_arg) {
  return rag_pb.CodeSearchResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_search_KOncallQuerySearchRequest(arg) {
  if (!(arg instanceof rag_pb.KOncallQuerySearchRequest)) {
    throw new Error('Expected argument of type kwaipilot.search.KOncallQuerySearchRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_search_KOncallQuerySearchRequest(buffer_arg) {
  return rag_pb.KOncallQuerySearchRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_search_KOncallQuerySearchResponse(arg) {
  if (!(arg instanceof rag_pb.KOncallQuerySearchResponse)) {
    throw new Error('Expected argument of type kwaipilot.search.KOncallQuerySearchResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_search_KOncallQuerySearchResponse(buffer_arg) {
  return rag_pb.KOncallQuerySearchResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_search_KnowledgeRepoDeleteRequest(arg) {
  if (!(arg instanceof rag_pb.KnowledgeRepoDeleteRequest)) {
    throw new Error('Expected argument of type kwaipilot.search.KnowledgeRepoDeleteRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_search_KnowledgeRepoDeleteRequest(buffer_arg) {
  return rag_pb.KnowledgeRepoDeleteRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_search_KnowledgeRepoDeleteResponse(arg) {
  if (!(arg instanceof rag_pb.KnowledgeRepoDeleteResponse)) {
    throw new Error('Expected argument of type kwaipilot.search.KnowledgeRepoDeleteResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_search_KnowledgeRepoDeleteResponse(buffer_arg) {
  return rag_pb.KnowledgeRepoDeleteResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_search_KnowledgeRepoSearchRequest(arg) {
  if (!(arg instanceof rag_pb.KnowledgeRepoSearchRequest)) {
    throw new Error('Expected argument of type kwaipilot.search.KnowledgeRepoSearchRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_search_KnowledgeRepoSearchRequest(buffer_arg) {
  return rag_pb.KnowledgeRepoSearchRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_search_KnowledgeRepoSearchResponse(arg) {
  if (!(arg instanceof rag_pb.KnowledgeRepoSearchResponse)) {
    throw new Error('Expected argument of type kwaipilot.search.KnowledgeRepoSearchResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_search_KnowledgeRepoSearchResponse(buffer_arg) {
  return rag_pb.KnowledgeRepoSearchResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_search_KnowledgeRepoStoreRequest(arg) {
  if (!(arg instanceof rag_pb.KnowledgeRepoStoreRequest)) {
    throw new Error('Expected argument of type kwaipilot.search.KnowledgeRepoStoreRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_search_KnowledgeRepoStoreRequest(buffer_arg) {
  return rag_pb.KnowledgeRepoStoreRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_search_KnowledgeRepoStoreResponse(arg) {
  if (!(arg instanceof rag_pb.KnowledgeRepoStoreResponse)) {
    throw new Error('Expected argument of type kwaipilot.search.KnowledgeRepoStoreResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_search_KnowledgeRepoStoreResponse(buffer_arg) {
  return rag_pb.KnowledgeRepoStoreResponse.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_search_WebSearchRequest(arg) {
  if (!(arg instanceof rag_pb.WebSearchRequest)) {
    throw new Error('Expected argument of type kwaipilot.search.WebSearchRequest');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_search_WebSearchRequest(buffer_arg) {
  return rag_pb.WebSearchRequest.deserializeBinary(new Uint8Array(buffer_arg));
}

function serialize_kwaipilot_search_WebSearchResponse(arg) {
  if (!(arg instanceof rag_pb.WebSearchResponse)) {
    throw new Error('Expected argument of type kwaipilot.search.WebSearchResponse');
  }
  return Buffer.from(arg.serializeBinary());
}

function deserialize_kwaipilot_search_WebSearchResponse(buffer_arg) {
  return rag_pb.WebSearchResponse.deserializeBinary(new Uint8Array(buffer_arg));
}


var KwaipilotSearchServiceService = exports.KwaipilotSearchServiceService = {
  knowledgeRepoSearch: {
    path: '/kwaipilot.search.KwaipilotSearchService/KnowledgeRepoSearch',
    requestStream: false,
    responseStream: false,
    requestType: rag_pb.KnowledgeRepoSearchRequest,
    responseType: rag_pb.KnowledgeRepoSearchResponse,
    requestSerialize: serialize_kwaipilot_search_KnowledgeRepoSearchRequest,
    requestDeserialize: deserialize_kwaipilot_search_KnowledgeRepoSearchRequest,
    responseSerialize: serialize_kwaipilot_search_KnowledgeRepoSearchResponse,
    responseDeserialize: deserialize_kwaipilot_search_KnowledgeRepoSearchResponse,
  },
  // 知识库检索
codeSearch: {
    path: '/kwaipilot.search.KwaipilotSearchService/CodeSearch',
    requestStream: false,
    responseStream: false,
    requestType: rag_pb.CodeSearchRequest,
    responseType: rag_pb.CodeSearchResponse,
    requestSerialize: serialize_kwaipilot_search_CodeSearchRequest,
    requestDeserialize: deserialize_kwaipilot_search_CodeSearchRequest,
    responseSerialize: serialize_kwaipilot_search_CodeSearchResponse,
    responseDeserialize: deserialize_kwaipilot_search_CodeSearchResponse,
  },
  // 代码搜索
knowledgeRepoStore: {
    path: '/kwaipilot.search.KwaipilotSearchService/KnowledgeRepoStore',
    requestStream: false,
    responseStream: false,
    requestType: rag_pb.KnowledgeRepoStoreRequest,
    responseType: rag_pb.KnowledgeRepoStoreResponse,
    requestSerialize: serialize_kwaipilot_search_KnowledgeRepoStoreRequest,
    requestDeserialize: deserialize_kwaipilot_search_KnowledgeRepoStoreRequest,
    responseSerialize: serialize_kwaipilot_search_KnowledgeRepoStoreResponse,
    responseDeserialize: deserialize_kwaipilot_search_KnowledgeRepoStoreResponse,
  },
  // 知识库存储，超时
knowledgeRepoDelete: {
    path: '/kwaipilot.search.KwaipilotSearchService/KnowledgeRepoDelete',
    requestStream: false,
    responseStream: false,
    requestType: rag_pb.KnowledgeRepoDeleteRequest,
    responseType: rag_pb.KnowledgeRepoDeleteResponse,
    requestSerialize: serialize_kwaipilot_search_KnowledgeRepoDeleteRequest,
    requestDeserialize: deserialize_kwaipilot_search_KnowledgeRepoDeleteRequest,
    responseSerialize: serialize_kwaipilot_search_KnowledgeRepoDeleteResponse,
    responseDeserialize: deserialize_kwaipilot_search_KnowledgeRepoDeleteResponse,
  },
  // 知识库删除文档
koncallQuerySearch: {
    path: '/kwaipilot.search.KwaipilotSearchService/KoncallQuerySearch',
    requestStream: false,
    responseStream: false,
    requestType: rag_pb.KOncallQuerySearchRequest,
    responseType: rag_pb.KOncallQuerySearchResponse,
    requestSerialize: serialize_kwaipilot_search_KOncallQuerySearchRequest,
    requestDeserialize: deserialize_kwaipilot_search_KOncallQuerySearchRequest,
    responseSerialize: serialize_kwaipilot_search_KOncallQuerySearchResponse,
    responseDeserialize: deserialize_kwaipilot_search_KOncallQuerySearchResponse,
  },
  webSearch: {
    path: '/kwaipilot.search.KwaipilotSearchService/WebSearch',
    requestStream: false,
    responseStream: false,
    requestType: rag_pb.WebSearchRequest,
    responseType: rag_pb.WebSearchResponse,
    requestSerialize: serialize_kwaipilot_search_WebSearchRequest,
    requestDeserialize: deserialize_kwaipilot_search_WebSearchRequest,
    responseSerialize: serialize_kwaipilot_search_WebSearchResponse,
    responseDeserialize: deserialize_kwaipilot_search_WebSearchResponse,
  },
};

exports.KwaipilotSearchServiceClient = grpc.makeGenericClientConstructor(KwaipilotSearchServiceService);
