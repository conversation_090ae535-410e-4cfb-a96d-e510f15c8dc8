// source: rag.proto
/**
 * @fileoverview
 * @enhanceable
 * @suppress {messageConventions} JS Compiler reports an error if a variable or
 *     field starts with 'MSG_' and isn't a translatable message.
 * @public
 */
// GENERATED CODE -- DO NOT EDIT!
/* eslint-disable */
// @ts-nocheck

var jspb = require('google-protobuf');
var goog = jspb;
var global = Function('return this')();

goog.exportSymbol('proto.kwaipilot.search.ChatFile', null, global);
goog.exportSymbol('proto.kwaipilot.search.ChatMessage', null, global);
goog.exportSymbol('proto.kwaipilot.search.CodeSearchRequest', null, global);
goog.exportSymbol('proto.kwaipilot.search.CodeSearchResponse', null, global);
goog.exportSymbol('proto.kwaipilot.search.CodeSearchResult', null, global);
goog.exportSymbol('proto.kwaipilot.search.Doc', null, global);
goog.exportSymbol('proto.kwaipilot.search.KOncallQuerySearchRequest', null, global);
goog.exportSymbol('proto.kwaipilot.search.KOncallQuerySearchResponse', null, global);
goog.exportSymbol('proto.kwaipilot.search.Knowledge', null, global);
goog.exportSymbol('proto.kwaipilot.search.KnowledgeRepoDeleteRequest', null, global);
goog.exportSymbol('proto.kwaipilot.search.KnowledgeRepoDeleteResponse', null, global);
goog.exportSymbol('proto.kwaipilot.search.KnowledgeRepoSearchRequest', null, global);
goog.exportSymbol('proto.kwaipilot.search.KnowledgeRepoSearchResponse', null, global);
goog.exportSymbol('proto.kwaipilot.search.KnowledgeRepoStoreRequest', null, global);
goog.exportSymbol('proto.kwaipilot.search.KnowledgeRepoStoreResponse', null, global);
goog.exportSymbol('proto.kwaipilot.search.OperationResult', null, global);
goog.exportSymbol('proto.kwaipilot.search.SimilarQueries', null, global);
goog.exportSymbol('proto.kwaipilot.search.UploadedFile', null, global);
goog.exportSymbol('proto.kwaipilot.search.WebSearchRequest', null, global);
goog.exportSymbol('proto.kwaipilot.search.WebSearchResponse', null, global);
goog.exportSymbol('proto.kwaipilot.search.WebSnippet', null, global);
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.OperationResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.search.OperationResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.OperationResult.displayName = 'proto.kwaipilot.search.OperationResult';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.search.KnowledgeRepoDeleteRequest.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.search.KnowledgeRepoDeleteRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.KnowledgeRepoDeleteRequest.displayName = 'proto.kwaipilot.search.KnowledgeRepoDeleteRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.KnowledgeRepoDeleteResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.search.KnowledgeRepoDeleteResponse.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.search.KnowledgeRepoDeleteResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.KnowledgeRepoDeleteResponse.displayName = 'proto.kwaipilot.search.KnowledgeRepoDeleteResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.Doc = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.search.Doc, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.Doc.displayName = 'proto.kwaipilot.search.Doc';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.search.KnowledgeRepoStoreRequest.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.search.KnowledgeRepoStoreRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.KnowledgeRepoStoreRequest.displayName = 'proto.kwaipilot.search.KnowledgeRepoStoreRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.KnowledgeRepoStoreResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.search.KnowledgeRepoStoreResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.KnowledgeRepoStoreResponse.displayName = 'proto.kwaipilot.search.KnowledgeRepoStoreResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.search.KnowledgeRepoSearchRequest.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.search.KnowledgeRepoSearchRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.KnowledgeRepoSearchRequest.displayName = 'proto.kwaipilot.search.KnowledgeRepoSearchRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.KOncallQuerySearchRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.search.KOncallQuerySearchRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.KOncallQuerySearchRequest.displayName = 'proto.kwaipilot.search.KOncallQuerySearchRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.KOncallQuerySearchResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.search.KOncallQuerySearchResponse.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.search.KOncallQuerySearchResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.KOncallQuerySearchResponse.displayName = 'proto.kwaipilot.search.KOncallQuerySearchResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.SimilarQueries = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.search.SimilarQueries, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.SimilarQueries.displayName = 'proto.kwaipilot.search.SimilarQueries';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.ChatMessage = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.search.ChatMessage, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.ChatMessage.displayName = 'proto.kwaipilot.search.ChatMessage';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.Knowledge = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.search.Knowledge, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.Knowledge.displayName = 'proto.kwaipilot.search.Knowledge';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.KnowledgeRepoSearchResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.search.KnowledgeRepoSearchResponse.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.search.KnowledgeRepoSearchResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.KnowledgeRepoSearchResponse.displayName = 'proto.kwaipilot.search.KnowledgeRepoSearchResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.ChatFile = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.search.ChatFile, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.ChatFile.displayName = 'proto.kwaipilot.search.ChatFile';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.CodeSearchRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.search.CodeSearchRequest.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.search.CodeSearchRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.CodeSearchRequest.displayName = 'proto.kwaipilot.search.CodeSearchRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.CodeSearchResult = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.search.CodeSearchResult, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.CodeSearchResult.displayName = 'proto.kwaipilot.search.CodeSearchResult';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.CodeSearchResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.search.CodeSearchResponse.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.search.CodeSearchResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.CodeSearchResponse.displayName = 'proto.kwaipilot.search.CodeSearchResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.UploadedFile = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.search.UploadedFile, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.UploadedFile.displayName = 'proto.kwaipilot.search.UploadedFile';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.WebSearchRequest = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.search.WebSearchRequest.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.search.WebSearchRequest, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.WebSearchRequest.displayName = 'proto.kwaipilot.search.WebSearchRequest';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.WebSearchResponse = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, proto.kwaipilot.search.WebSearchResponse.repeatedFields_, null);
};
goog.inherits(proto.kwaipilot.search.WebSearchResponse, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.WebSearchResponse.displayName = 'proto.kwaipilot.search.WebSearchResponse';
}
/**
 * Generated by JsPbCodeGenerator.
 * @param {Array=} opt_data Optional initial data array, typically from a
 * server response, or constructed directly in Javascript. The array is used
 * in place and becomes part of the constructed object. It is not cloned.
 * If no data is provided, the constructed object will be empty, but still
 * valid.
 * @extends {jspb.Message}
 * @constructor
 */
proto.kwaipilot.search.WebSnippet = function(opt_data) {
  jspb.Message.initialize(this, opt_data, 0, -1, null, null);
};
goog.inherits(proto.kwaipilot.search.WebSnippet, jspb.Message);
if (goog.DEBUG && !COMPILED) {
  /**
   * @public
   * @override
   */
  proto.kwaipilot.search.WebSnippet.displayName = 'proto.kwaipilot.search.WebSnippet';
}



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.OperationResult.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.OperationResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.OperationResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.OperationResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    link: jspb.Message.getFieldWithDefault(msg, 1, ""),
    result: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.OperationResult}
 */
proto.kwaipilot.search.OperationResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.OperationResult;
  return proto.kwaipilot.search.OperationResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.OperationResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.OperationResult}
 */
proto.kwaipilot.search.OperationResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setLink(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setResult(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.OperationResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.OperationResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.OperationResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.OperationResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getLink();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getResult();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string link = 1;
 * @return {string}
 */
proto.kwaipilot.search.OperationResult.prototype.getLink = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.OperationResult} returns this
 */
proto.kwaipilot.search.OperationResult.prototype.setLink = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string result = 2;
 * @return {string}
 */
proto.kwaipilot.search.OperationResult.prototype.getResult = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.OperationResult} returns this
 */
proto.kwaipilot.search.OperationResult.prototype.setResult = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest.repeatedFields_ = [3];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.KnowledgeRepoDeleteRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.KnowledgeRepoDeleteRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    knowledgeRepoId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    knowledgeRepoName: jspb.Message.getFieldWithDefault(msg, 2, ""),
    linksList: (f = jspb.Message.getRepeatedField(msg, 3)) == null ? undefined : f
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.KnowledgeRepoDeleteRequest}
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.KnowledgeRepoDeleteRequest;
  return proto.kwaipilot.search.KnowledgeRepoDeleteRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.KnowledgeRepoDeleteRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.KnowledgeRepoDeleteRequest}
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setKnowledgeRepoId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setKnowledgeRepoName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.addLinks(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.KnowledgeRepoDeleteRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.KnowledgeRepoDeleteRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getKnowledgeRepoId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getKnowledgeRepoName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getLinksList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      3,
      f
    );
  }
};


/**
 * optional string knowledge_repo_id = 1;
 * @return {string}
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest.prototype.getKnowledgeRepoId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoDeleteRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest.prototype.setKnowledgeRepoId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string knowledge_repo_name = 2;
 * @return {string}
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest.prototype.getKnowledgeRepoName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoDeleteRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest.prototype.setKnowledgeRepoName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated string links = 3;
 * @return {!Array<string>}
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest.prototype.getLinksList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 3));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoDeleteRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest.prototype.setLinksList = function(value) {
  return jspb.Message.setField(this, 3, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.search.KnowledgeRepoDeleteRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest.prototype.addLinks = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.search.KnowledgeRepoDeleteRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoDeleteRequest.prototype.clearLinksList = function() {
  return this.setLinksList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.search.KnowledgeRepoDeleteResponse.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.KnowledgeRepoDeleteResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.KnowledgeRepoDeleteResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.KnowledgeRepoDeleteResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KnowledgeRepoDeleteResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    resultsList: jspb.Message.toObjectList(msg.getResultsList(),
    proto.kwaipilot.search.OperationResult.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.KnowledgeRepoDeleteResponse}
 */
proto.kwaipilot.search.KnowledgeRepoDeleteResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.KnowledgeRepoDeleteResponse;
  return proto.kwaipilot.search.KnowledgeRepoDeleteResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.KnowledgeRepoDeleteResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.KnowledgeRepoDeleteResponse}
 */
proto.kwaipilot.search.KnowledgeRepoDeleteResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.kwaipilot.search.OperationResult;
      reader.readMessage(value,proto.kwaipilot.search.OperationResult.deserializeBinaryFromReader);
      msg.addResults(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.KnowledgeRepoDeleteResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.KnowledgeRepoDeleteResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.KnowledgeRepoDeleteResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KnowledgeRepoDeleteResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getResultsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.kwaipilot.search.OperationResult.serializeBinaryToWriter
    );
  }
};


/**
 * repeated OperationResult results = 1;
 * @return {!Array<!proto.kwaipilot.search.OperationResult>}
 */
proto.kwaipilot.search.KnowledgeRepoDeleteResponse.prototype.getResultsList = function() {
  return /** @type{!Array<!proto.kwaipilot.search.OperationResult>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.search.OperationResult, 1));
};


/**
 * @param {!Array<!proto.kwaipilot.search.OperationResult>} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoDeleteResponse} returns this
*/
proto.kwaipilot.search.KnowledgeRepoDeleteResponse.prototype.setResultsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.kwaipilot.search.OperationResult=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.search.OperationResult}
 */
proto.kwaipilot.search.KnowledgeRepoDeleteResponse.prototype.addResults = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.kwaipilot.search.OperationResult, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.search.KnowledgeRepoDeleteResponse} returns this
 */
proto.kwaipilot.search.KnowledgeRepoDeleteResponse.prototype.clearResultsList = function() {
  return this.setResultsList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.Doc.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.Doc.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.Doc} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.Doc.toObject = function(includeInstance, msg) {
  var f, obj = {
    name: jspb.Message.getFieldWithDefault(msg, 1, ""),
    link: jspb.Message.getFieldWithDefault(msg, 2, ""),
    sourceType: jspb.Message.getFieldWithDefault(msg, 3, ""),
    status: jspb.Message.getFieldWithDefault(msg, 4, 0),
    content: jspb.Message.getFieldWithDefault(msg, 5, ""),
    id: jspb.Message.getFieldWithDefault(msg, 6, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.Doc}
 */
proto.kwaipilot.search.Doc.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.Doc;
  return proto.kwaipilot.search.Doc.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.Doc} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.Doc}
 */
proto.kwaipilot.search.Doc.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setLink(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setSourceType(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setStatus(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setContent(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readInt64());
      msg.setId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.Doc.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.Doc.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.Doc} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.Doc.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getLink();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getSourceType();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getStatus();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getContent();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getId();
  if (f !== 0) {
    writer.writeInt64(
      6,
      f
    );
  }
};


/**
 * optional string name = 1;
 * @return {string}
 */
proto.kwaipilot.search.Doc.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.Doc} returns this
 */
proto.kwaipilot.search.Doc.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string link = 2;
 * @return {string}
 */
proto.kwaipilot.search.Doc.prototype.getLink = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.Doc} returns this
 */
proto.kwaipilot.search.Doc.prototype.setLink = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string source_type = 3;
 * @return {string}
 */
proto.kwaipilot.search.Doc.prototype.getSourceType = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.Doc} returns this
 */
proto.kwaipilot.search.Doc.prototype.setSourceType = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional int32 status = 4;
 * @return {number}
 */
proto.kwaipilot.search.Doc.prototype.getStatus = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.search.Doc} returns this
 */
proto.kwaipilot.search.Doc.prototype.setStatus = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional string content = 5;
 * @return {string}
 */
proto.kwaipilot.search.Doc.prototype.getContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.Doc} returns this
 */
proto.kwaipilot.search.Doc.prototype.setContent = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional int64 id = 6;
 * @return {number}
 */
proto.kwaipilot.search.Doc.prototype.getId = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 6, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.search.Doc} returns this
 */
proto.kwaipilot.search.Doc.prototype.setId = function(value) {
  return jspb.Message.setProto3IntField(this, 6, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.KnowledgeRepoStoreRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.KnowledgeRepoStoreRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    docsList: jspb.Message.toObjectList(msg.getDocsList(),
    proto.kwaipilot.search.Doc.toObject, includeInstance),
    knowledgeRepoId: jspb.Message.getFieldWithDefault(msg, 2, ""),
    knowledgeRepoName: jspb.Message.getFieldWithDefault(msg, 3, ""),
    requestId: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.KnowledgeRepoStoreRequest}
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.KnowledgeRepoStoreRequest;
  return proto.kwaipilot.search.KnowledgeRepoStoreRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.KnowledgeRepoStoreRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.KnowledgeRepoStoreRequest}
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.kwaipilot.search.Doc;
      reader.readMessage(value,proto.kwaipilot.search.Doc.deserializeBinaryFromReader);
      msg.addDocs(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setKnowledgeRepoId(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setKnowledgeRepoName(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setRequestId(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.KnowledgeRepoStoreRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.KnowledgeRepoStoreRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getDocsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.kwaipilot.search.Doc.serializeBinaryToWriter
    );
  }
  f = message.getKnowledgeRepoId();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getKnowledgeRepoName();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getRequestId();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * repeated Doc docs = 1;
 * @return {!Array<!proto.kwaipilot.search.Doc>}
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.prototype.getDocsList = function() {
  return /** @type{!Array<!proto.kwaipilot.search.Doc>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.search.Doc, 1));
};


/**
 * @param {!Array<!proto.kwaipilot.search.Doc>} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoStoreRequest} returns this
*/
proto.kwaipilot.search.KnowledgeRepoStoreRequest.prototype.setDocsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.kwaipilot.search.Doc=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.search.Doc}
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.prototype.addDocs = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.kwaipilot.search.Doc, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.search.KnowledgeRepoStoreRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.prototype.clearDocsList = function() {
  return this.setDocsList([]);
};


/**
 * optional string knowledge_repo_id = 2;
 * @return {string}
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.prototype.getKnowledgeRepoId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoStoreRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.prototype.setKnowledgeRepoId = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string knowledge_repo_name = 3;
 * @return {string}
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.prototype.getKnowledgeRepoName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoStoreRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.prototype.setKnowledgeRepoName = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string request_id = 4;
 * @return {string}
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.prototype.getRequestId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoStoreRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoStoreRequest.prototype.setRequestId = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.KnowledgeRepoStoreResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.KnowledgeRepoStoreResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.KnowledgeRepoStoreResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KnowledgeRepoStoreResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    status: jspb.Message.getFieldWithDefault(msg, 1, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.KnowledgeRepoStoreResponse}
 */
proto.kwaipilot.search.KnowledgeRepoStoreResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.KnowledgeRepoStoreResponse;
  return proto.kwaipilot.search.KnowledgeRepoStoreResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.KnowledgeRepoStoreResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.KnowledgeRepoStoreResponse}
 */
proto.kwaipilot.search.KnowledgeRepoStoreResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setStatus(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.KnowledgeRepoStoreResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.KnowledgeRepoStoreResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.KnowledgeRepoStoreResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KnowledgeRepoStoreResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getStatus();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
};


/**
 * optional string status = 1;
 * @return {string}
 */
proto.kwaipilot.search.KnowledgeRepoStoreResponse.prototype.getStatus = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoStoreResponse} returns this
 */
proto.kwaipilot.search.KnowledgeRepoStoreResponse.prototype.setStatus = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.repeatedFields_ = [2,5];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.KnowledgeRepoSearchRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.KnowledgeRepoSearchRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    platform: jspb.Message.getFieldWithDefault(msg, 1, ""),
    knowledgeRepoIdsList: (f = jspb.Message.getRepeatedField(msg, 2)) == null ? undefined : f,
    query: jspb.Message.getFieldWithDefault(msg, 3, ""),
    topK: jspb.Message.getFieldWithDefault(msg, 4, 0),
    chatHistoryList: jspb.Message.toObjectList(msg.getChatHistoryList(),
    proto.kwaipilot.search.ChatMessage.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.KnowledgeRepoSearchRequest}
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.KnowledgeRepoSearchRequest;
  return proto.kwaipilot.search.KnowledgeRepoSearchRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.KnowledgeRepoSearchRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.KnowledgeRepoSearchRequest}
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setPlatform(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.addKnowledgeRepoIds(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setQuery(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setTopK(value);
      break;
    case 5:
      var value = new proto.kwaipilot.search.ChatMessage;
      reader.readMessage(value,proto.kwaipilot.search.ChatMessage.deserializeBinaryFromReader);
      msg.addChatHistory(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.KnowledgeRepoSearchRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.KnowledgeRepoSearchRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getPlatform();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getKnowledgeRepoIdsList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      2,
      f
    );
  }
  f = message.getQuery();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getTopK();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getChatHistoryList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      5,
      f,
      proto.kwaipilot.search.ChatMessage.serializeBinaryToWriter
    );
  }
};


/**
 * optional string platform = 1;
 * @return {string}
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.getPlatform = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.setPlatform = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * repeated string knowledge_repo_ids = 2;
 * @return {!Array<string>}
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.getKnowledgeRepoIdsList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 2));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.setKnowledgeRepoIdsList = function(value) {
  return jspb.Message.setField(this, 2, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.search.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.addKnowledgeRepoIds = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 2, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.search.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.clearKnowledgeRepoIdsList = function() {
  return this.setKnowledgeRepoIdsList([]);
};


/**
 * optional string query = 3;
 * @return {string}
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.getQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.setQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional int32 top_k = 4;
 * @return {number}
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.getTopK = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.setTopK = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * repeated ChatMessage chat_history = 5;
 * @return {!Array<!proto.kwaipilot.search.ChatMessage>}
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.getChatHistoryList = function() {
  return /** @type{!Array<!proto.kwaipilot.search.ChatMessage>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.search.ChatMessage, 5));
};


/**
 * @param {!Array<!proto.kwaipilot.search.ChatMessage>} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoSearchRequest} returns this
*/
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.setChatHistoryList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 5, value);
};


/**
 * @param {!proto.kwaipilot.search.ChatMessage=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.search.ChatMessage}
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.addChatHistory = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 5, opt_value, proto.kwaipilot.search.ChatMessage, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.search.KnowledgeRepoSearchRequest} returns this
 */
proto.kwaipilot.search.KnowledgeRepoSearchRequest.prototype.clearChatHistoryList = function() {
  return this.setChatHistoryList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.KOncallQuerySearchRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.KOncallQuerySearchRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    queryId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    query: jspb.Message.getFieldWithDefault(msg, 2, ""),
    collection: jspb.Message.getFieldWithDefault(msg, 3, ""),
    retrieveCnt: jspb.Message.getFieldWithDefault(msg, 4, 0),
    rerankCnt: jspb.Message.getFieldWithDefault(msg, 5, 0),
    denseRatio: jspb.Message.getFloatingPointFieldWithDefault(msg, 6, 0.0),
    threshold: jspb.Message.getFloatingPointFieldWithDefault(msg, 7, 0.0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.KOncallQuerySearchRequest}
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.KOncallQuerySearchRequest;
  return proto.kwaipilot.search.KOncallQuerySearchRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.KOncallQuerySearchRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.KOncallQuerySearchRequest}
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setQueryId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setQuery(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setCollection(value);
      break;
    case 4:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRetrieveCnt(value);
      break;
    case 5:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setRerankCnt(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setDenseRatio(value);
      break;
    case 7:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setThreshold(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.KOncallQuerySearchRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.KOncallQuerySearchRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getQueryId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getQuery();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getCollection();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getRetrieveCnt();
  if (f !== 0) {
    writer.writeInt32(
      4,
      f
    );
  }
  f = message.getRerankCnt();
  if (f !== 0) {
    writer.writeInt32(
      5,
      f
    );
  }
  f = message.getDenseRatio();
  if (f !== 0.0) {
    writer.writeFloat(
      6,
      f
    );
  }
  f = message.getThreshold();
  if (f !== 0.0) {
    writer.writeFloat(
      7,
      f
    );
  }
};


/**
 * optional string query_id = 1;
 * @return {string}
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.getQueryId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.KOncallQuerySearchRequest} returns this
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.setQueryId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string query = 2;
 * @return {string}
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.getQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.KOncallQuerySearchRequest} returns this
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.setQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string collection = 3;
 * @return {string}
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.getCollection = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.KOncallQuerySearchRequest} returns this
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.setCollection = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional int32 retrieve_cnt = 4;
 * @return {number}
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.getRetrieveCnt = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 4, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.search.KOncallQuerySearchRequest} returns this
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.setRetrieveCnt = function(value) {
  return jspb.Message.setProto3IntField(this, 4, value);
};


/**
 * optional int32 rerank_cnt = 5;
 * @return {number}
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.getRerankCnt = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 5, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.search.KOncallQuerySearchRequest} returns this
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.setRerankCnt = function(value) {
  return jspb.Message.setProto3IntField(this, 5, value);
};


/**
 * optional float dense_ratio = 6;
 * @return {number}
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.getDenseRatio = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 6, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.search.KOncallQuerySearchRequest} returns this
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.setDenseRatio = function(value) {
  return jspb.Message.setProto3FloatField(this, 6, value);
};


/**
 * optional float threshold = 7;
 * @return {number}
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.getThreshold = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 7, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.search.KOncallQuerySearchRequest} returns this
 */
proto.kwaipilot.search.KOncallQuerySearchRequest.prototype.setThreshold = function(value) {
  return jspb.Message.setProto3FloatField(this, 7, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.search.KOncallQuerySearchResponse.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.KOncallQuerySearchResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.KOncallQuerySearchResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.KOncallQuerySearchResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KOncallQuerySearchResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    similarQueriesList: jspb.Message.toObjectList(msg.getSimilarQueriesList(),
    proto.kwaipilot.search.SimilarQueries.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.KOncallQuerySearchResponse}
 */
proto.kwaipilot.search.KOncallQuerySearchResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.KOncallQuerySearchResponse;
  return proto.kwaipilot.search.KOncallQuerySearchResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.KOncallQuerySearchResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.KOncallQuerySearchResponse}
 */
proto.kwaipilot.search.KOncallQuerySearchResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.kwaipilot.search.SimilarQueries;
      reader.readMessage(value,proto.kwaipilot.search.SimilarQueries.deserializeBinaryFromReader);
      msg.addSimilarQueries(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.KOncallQuerySearchResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.KOncallQuerySearchResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.KOncallQuerySearchResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KOncallQuerySearchResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getSimilarQueriesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.kwaipilot.search.SimilarQueries.serializeBinaryToWriter
    );
  }
};


/**
 * repeated SimilarQueries similar_queries = 1;
 * @return {!Array<!proto.kwaipilot.search.SimilarQueries>}
 */
proto.kwaipilot.search.KOncallQuerySearchResponse.prototype.getSimilarQueriesList = function() {
  return /** @type{!Array<!proto.kwaipilot.search.SimilarQueries>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.search.SimilarQueries, 1));
};


/**
 * @param {!Array<!proto.kwaipilot.search.SimilarQueries>} value
 * @return {!proto.kwaipilot.search.KOncallQuerySearchResponse} returns this
*/
proto.kwaipilot.search.KOncallQuerySearchResponse.prototype.setSimilarQueriesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.kwaipilot.search.SimilarQueries=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.search.SimilarQueries}
 */
proto.kwaipilot.search.KOncallQuerySearchResponse.prototype.addSimilarQueries = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.kwaipilot.search.SimilarQueries, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.search.KOncallQuerySearchResponse} returns this
 */
proto.kwaipilot.search.KOncallQuerySearchResponse.prototype.clearSimilarQueriesList = function() {
  return this.setSimilarQueriesList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.SimilarQueries.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.SimilarQueries.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.SimilarQueries} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.SimilarQueries.toObject = function(includeInstance, msg) {
  var f, obj = {
    queryId: jspb.Message.getFieldWithDefault(msg, 1, ""),
    query: jspb.Message.getFieldWithDefault(msg, 2, ""),
    score: jspb.Message.getFloatingPointFieldWithDefault(msg, 3, 0.0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.SimilarQueries}
 */
proto.kwaipilot.search.SimilarQueries.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.SimilarQueries;
  return proto.kwaipilot.search.SimilarQueries.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.SimilarQueries} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.SimilarQueries}
 */
proto.kwaipilot.search.SimilarQueries.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setQueryId(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setQuery(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setScore(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.SimilarQueries.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.SimilarQueries.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.SimilarQueries} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.SimilarQueries.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getQueryId();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getQuery();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getScore();
  if (f !== 0.0) {
    writer.writeFloat(
      3,
      f
    );
  }
};


/**
 * optional string query_id = 1;
 * @return {string}
 */
proto.kwaipilot.search.SimilarQueries.prototype.getQueryId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.SimilarQueries} returns this
 */
proto.kwaipilot.search.SimilarQueries.prototype.setQueryId = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string query = 2;
 * @return {string}
 */
proto.kwaipilot.search.SimilarQueries.prototype.getQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.SimilarQueries} returns this
 */
proto.kwaipilot.search.SimilarQueries.prototype.setQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional float score = 3;
 * @return {number}
 */
proto.kwaipilot.search.SimilarQueries.prototype.getScore = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 3, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.search.SimilarQueries} returns this
 */
proto.kwaipilot.search.SimilarQueries.prototype.setScore = function(value) {
  return jspb.Message.setProto3FloatField(this, 3, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.ChatMessage.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.ChatMessage.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.ChatMessage} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.ChatMessage.toObject = function(includeInstance, msg) {
  var f, obj = {
    role: jspb.Message.getFieldWithDefault(msg, 1, ""),
    content: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.ChatMessage}
 */
proto.kwaipilot.search.ChatMessage.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.ChatMessage;
  return proto.kwaipilot.search.ChatMessage.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.ChatMessage} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.ChatMessage}
 */
proto.kwaipilot.search.ChatMessage.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setRole(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.ChatMessage.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.ChatMessage.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.ChatMessage} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.ChatMessage.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRole();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getContent();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * optional string role = 1;
 * @return {string}
 */
proto.kwaipilot.search.ChatMessage.prototype.getRole = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.ChatMessage} returns this
 */
proto.kwaipilot.search.ChatMessage.prototype.setRole = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string content = 2;
 * @return {string}
 */
proto.kwaipilot.search.ChatMessage.prototype.getContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.ChatMessage} returns this
 */
proto.kwaipilot.search.ChatMessage.prototype.setContent = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.Knowledge.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.Knowledge.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.Knowledge} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.Knowledge.toObject = function(includeInstance, msg) {
  var f, obj = {
    content: jspb.Message.getFieldWithDefault(msg, 1, ""),
    link: jspb.Message.getFieldWithDefault(msg, 2, ""),
    title: jspb.Message.getFieldWithDefault(msg, 3, ""),
    knowledgeRepoId: jspb.Message.getFieldWithDefault(msg, 4, ""),
    knowledgeRepoName: jspb.Message.getFieldWithDefault(msg, 5, ""),
    score: jspb.Message.getFloatingPointFieldWithDefault(msg, 6, 0.0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.Knowledge}
 */
proto.kwaipilot.search.Knowledge.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.Knowledge;
  return proto.kwaipilot.search.Knowledge.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.Knowledge} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.Knowledge}
 */
proto.kwaipilot.search.Knowledge.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setContent(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setLink(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setTitle(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setKnowledgeRepoId(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setKnowledgeRepoName(value);
      break;
    case 6:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setScore(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.Knowledge.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.Knowledge.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.Knowledge} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.Knowledge.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getContent();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getLink();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTitle();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getKnowledgeRepoId();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getKnowledgeRepoName();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getScore();
  if (f !== 0.0) {
    writer.writeFloat(
      6,
      f
    );
  }
};


/**
 * optional string content = 1;
 * @return {string}
 */
proto.kwaipilot.search.Knowledge.prototype.getContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.Knowledge} returns this
 */
proto.kwaipilot.search.Knowledge.prototype.setContent = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string link = 2;
 * @return {string}
 */
proto.kwaipilot.search.Knowledge.prototype.getLink = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.Knowledge} returns this
 */
proto.kwaipilot.search.Knowledge.prototype.setLink = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string title = 3;
 * @return {string}
 */
proto.kwaipilot.search.Knowledge.prototype.getTitle = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.Knowledge} returns this
 */
proto.kwaipilot.search.Knowledge.prototype.setTitle = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string knowledge_repo_id = 4;
 * @return {string}
 */
proto.kwaipilot.search.Knowledge.prototype.getKnowledgeRepoId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.Knowledge} returns this
 */
proto.kwaipilot.search.Knowledge.prototype.setKnowledgeRepoId = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string knowledge_repo_name = 5;
 * @return {string}
 */
proto.kwaipilot.search.Knowledge.prototype.getKnowledgeRepoName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.Knowledge} returns this
 */
proto.kwaipilot.search.Knowledge.prototype.setKnowledgeRepoName = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional float score = 6;
 * @return {number}
 */
proto.kwaipilot.search.Knowledge.prototype.getScore = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 6, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.search.Knowledge} returns this
 */
proto.kwaipilot.search.Knowledge.prototype.setScore = function(value) {
  return jspb.Message.setProto3FloatField(this, 6, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.search.KnowledgeRepoSearchResponse.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.KnowledgeRepoSearchResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.KnowledgeRepoSearchResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.KnowledgeRepoSearchResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KnowledgeRepoSearchResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    knowledgesList: jspb.Message.toObjectList(msg.getKnowledgesList(),
    proto.kwaipilot.search.Knowledge.toObject, includeInstance),
    prompt: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.KnowledgeRepoSearchResponse}
 */
proto.kwaipilot.search.KnowledgeRepoSearchResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.KnowledgeRepoSearchResponse;
  return proto.kwaipilot.search.KnowledgeRepoSearchResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.KnowledgeRepoSearchResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.KnowledgeRepoSearchResponse}
 */
proto.kwaipilot.search.KnowledgeRepoSearchResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.kwaipilot.search.Knowledge;
      reader.readMessage(value,proto.kwaipilot.search.Knowledge.deserializeBinaryFromReader);
      msg.addKnowledges(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setPrompt(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.KnowledgeRepoSearchResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.KnowledgeRepoSearchResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.KnowledgeRepoSearchResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.KnowledgeRepoSearchResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getKnowledgesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.kwaipilot.search.Knowledge.serializeBinaryToWriter
    );
  }
  f = message.getPrompt();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * repeated Knowledge knowledges = 1;
 * @return {!Array<!proto.kwaipilot.search.Knowledge>}
 */
proto.kwaipilot.search.KnowledgeRepoSearchResponse.prototype.getKnowledgesList = function() {
  return /** @type{!Array<!proto.kwaipilot.search.Knowledge>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.search.Knowledge, 1));
};


/**
 * @param {!Array<!proto.kwaipilot.search.Knowledge>} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoSearchResponse} returns this
*/
proto.kwaipilot.search.KnowledgeRepoSearchResponse.prototype.setKnowledgesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.kwaipilot.search.Knowledge=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.search.Knowledge}
 */
proto.kwaipilot.search.KnowledgeRepoSearchResponse.prototype.addKnowledges = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.kwaipilot.search.Knowledge, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.search.KnowledgeRepoSearchResponse} returns this
 */
proto.kwaipilot.search.KnowledgeRepoSearchResponse.prototype.clearKnowledgesList = function() {
  return this.setKnowledgesList([]);
};


/**
 * optional string prompt = 2;
 * @return {string}
 */
proto.kwaipilot.search.KnowledgeRepoSearchResponse.prototype.getPrompt = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.KnowledgeRepoSearchResponse} returns this
 */
proto.kwaipilot.search.KnowledgeRepoSearchResponse.prototype.setPrompt = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.ChatFile.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.ChatFile.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.ChatFile} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.ChatFile.toObject = function(includeInstance, msg) {
  var f, obj = {
    code: jspb.Message.getFieldWithDefault(msg, 1, ""),
    name: jspb.Message.getFieldWithDefault(msg, 2, ""),
    language: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.ChatFile}
 */
proto.kwaipilot.search.ChatFile.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.ChatFile;
  return proto.kwaipilot.search.ChatFile.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.ChatFile} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.ChatFile}
 */
proto.kwaipilot.search.ChatFile.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setCode(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setName(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setLanguage(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.ChatFile.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.ChatFile.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.ChatFile} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.ChatFile.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCode();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getName();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getLanguage();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string code = 1;
 * @return {string}
 */
proto.kwaipilot.search.ChatFile.prototype.getCode = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.ChatFile} returns this
 */
proto.kwaipilot.search.ChatFile.prototype.setCode = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string name = 2;
 * @return {string}
 */
proto.kwaipilot.search.ChatFile.prototype.getName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.ChatFile} returns this
 */
proto.kwaipilot.search.ChatFile.prototype.setName = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string language = 3;
 * @return {string}
 */
proto.kwaipilot.search.ChatFile.prototype.getLanguage = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.ChatFile} returns this
 */
proto.kwaipilot.search.ChatFile.prototype.setLanguage = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.search.CodeSearchRequest.repeatedFields_ = [3,5];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.CodeSearchRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.CodeSearchRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.CodeSearchRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.CodeSearchRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    repoName: jspb.Message.getFieldWithDefault(msg, 1, ""),
    commitId: jspb.Message.getFieldWithDefault(msg, 2, ""),
    targetDirectoryList: (f = jspb.Message.getRepeatedField(msg, 3)) == null ? undefined : f,
    query: jspb.Message.getFieldWithDefault(msg, 4, ""),
    filesList: jspb.Message.toObjectList(msg.getFilesList(),
    proto.kwaipilot.search.ChatFile.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.CodeSearchRequest}
 */
proto.kwaipilot.search.CodeSearchRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.CodeSearchRequest;
  return proto.kwaipilot.search.CodeSearchRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.CodeSearchRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.CodeSearchRequest}
 */
proto.kwaipilot.search.CodeSearchRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setRepoName(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setCommitId(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.addTargetDirectory(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setQuery(value);
      break;
    case 5:
      var value = new proto.kwaipilot.search.ChatFile;
      reader.readMessage(value,proto.kwaipilot.search.ChatFile.deserializeBinaryFromReader);
      msg.addFiles(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.CodeSearchRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.CodeSearchRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.CodeSearchRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.CodeSearchRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getRepoName();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getCommitId();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getTargetDirectoryList();
  if (f.length > 0) {
    writer.writeRepeatedString(
      3,
      f
    );
  }
  f = message.getQuery();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getFilesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      5,
      f,
      proto.kwaipilot.search.ChatFile.serializeBinaryToWriter
    );
  }
};


/**
 * optional string repo_name = 1;
 * @return {string}
 */
proto.kwaipilot.search.CodeSearchRequest.prototype.getRepoName = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.CodeSearchRequest} returns this
 */
proto.kwaipilot.search.CodeSearchRequest.prototype.setRepoName = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string commit_id = 2;
 * @return {string}
 */
proto.kwaipilot.search.CodeSearchRequest.prototype.getCommitId = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.CodeSearchRequest} returns this
 */
proto.kwaipilot.search.CodeSearchRequest.prototype.setCommitId = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * repeated string target_directory = 3;
 * @return {!Array<string>}
 */
proto.kwaipilot.search.CodeSearchRequest.prototype.getTargetDirectoryList = function() {
  return /** @type {!Array<string>} */ (jspb.Message.getRepeatedField(this, 3));
};


/**
 * @param {!Array<string>} value
 * @return {!proto.kwaipilot.search.CodeSearchRequest} returns this
 */
proto.kwaipilot.search.CodeSearchRequest.prototype.setTargetDirectoryList = function(value) {
  return jspb.Message.setField(this, 3, value || []);
};


/**
 * @param {string} value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.search.CodeSearchRequest} returns this
 */
proto.kwaipilot.search.CodeSearchRequest.prototype.addTargetDirectory = function(value, opt_index) {
  return jspb.Message.addToRepeatedField(this, 3, value, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.search.CodeSearchRequest} returns this
 */
proto.kwaipilot.search.CodeSearchRequest.prototype.clearTargetDirectoryList = function() {
  return this.setTargetDirectoryList([]);
};


/**
 * optional string query = 4;
 * @return {string}
 */
proto.kwaipilot.search.CodeSearchRequest.prototype.getQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.CodeSearchRequest} returns this
 */
proto.kwaipilot.search.CodeSearchRequest.prototype.setQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * repeated ChatFile files = 5;
 * @return {!Array<!proto.kwaipilot.search.ChatFile>}
 */
proto.kwaipilot.search.CodeSearchRequest.prototype.getFilesList = function() {
  return /** @type{!Array<!proto.kwaipilot.search.ChatFile>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.search.ChatFile, 5));
};


/**
 * @param {!Array<!proto.kwaipilot.search.ChatFile>} value
 * @return {!proto.kwaipilot.search.CodeSearchRequest} returns this
*/
proto.kwaipilot.search.CodeSearchRequest.prototype.setFilesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 5, value);
};


/**
 * @param {!proto.kwaipilot.search.ChatFile=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.search.ChatFile}
 */
proto.kwaipilot.search.CodeSearchRequest.prototype.addFiles = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 5, opt_value, proto.kwaipilot.search.ChatFile, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.search.CodeSearchRequest} returns this
 */
proto.kwaipilot.search.CodeSearchRequest.prototype.clearFilesList = function() {
  return this.setFilesList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.CodeSearchResult.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.CodeSearchResult.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.CodeSearchResult} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.CodeSearchResult.toObject = function(includeInstance, msg) {
  var f, obj = {
    codeContent: jspb.Message.getFieldWithDefault(msg, 1, ""),
    score: jspb.Message.getFloatingPointFieldWithDefault(msg, 2, 0.0),
    metadata: jspb.Message.getFieldWithDefault(msg, 3, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.CodeSearchResult}
 */
proto.kwaipilot.search.CodeSearchResult.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.CodeSearchResult;
  return proto.kwaipilot.search.CodeSearchResult.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.CodeSearchResult} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.CodeSearchResult}
 */
proto.kwaipilot.search.CodeSearchResult.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setCodeContent(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readFloat());
      msg.setScore(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setMetadata(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.CodeSearchResult.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.CodeSearchResult.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.CodeSearchResult} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.CodeSearchResult.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCodeContent();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getScore();
  if (f !== 0.0) {
    writer.writeFloat(
      2,
      f
    );
  }
  f = message.getMetadata();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
};


/**
 * optional string code_content = 1;
 * @return {string}
 */
proto.kwaipilot.search.CodeSearchResult.prototype.getCodeContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.CodeSearchResult} returns this
 */
proto.kwaipilot.search.CodeSearchResult.prototype.setCodeContent = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional float score = 2;
 * @return {number}
 */
proto.kwaipilot.search.CodeSearchResult.prototype.getScore = function() {
  return /** @type {number} */ (jspb.Message.getFloatingPointFieldWithDefault(this, 2, 0.0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.search.CodeSearchResult} returns this
 */
proto.kwaipilot.search.CodeSearchResult.prototype.setScore = function(value) {
  return jspb.Message.setProto3FloatField(this, 2, value);
};


/**
 * optional string metadata = 3;
 * @return {string}
 */
proto.kwaipilot.search.CodeSearchResult.prototype.getMetadata = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.CodeSearchResult} returns this
 */
proto.kwaipilot.search.CodeSearchResult.prototype.setMetadata = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.search.CodeSearchResponse.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.CodeSearchResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.CodeSearchResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.CodeSearchResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.CodeSearchResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    codeSearchResultsList: jspb.Message.toObjectList(msg.getCodeSearchResultsList(),
    proto.kwaipilot.search.CodeSearchResult.toObject, includeInstance),
    prompt: jspb.Message.getFieldWithDefault(msg, 2, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.CodeSearchResponse}
 */
proto.kwaipilot.search.CodeSearchResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.CodeSearchResponse;
  return proto.kwaipilot.search.CodeSearchResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.CodeSearchResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.CodeSearchResponse}
 */
proto.kwaipilot.search.CodeSearchResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.kwaipilot.search.CodeSearchResult;
      reader.readMessage(value,proto.kwaipilot.search.CodeSearchResult.deserializeBinaryFromReader);
      msg.addCodeSearchResults(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setPrompt(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.CodeSearchResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.CodeSearchResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.CodeSearchResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.CodeSearchResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getCodeSearchResultsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.kwaipilot.search.CodeSearchResult.serializeBinaryToWriter
    );
  }
  f = message.getPrompt();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
};


/**
 * repeated CodeSearchResult code_search_results = 1;
 * @return {!Array<!proto.kwaipilot.search.CodeSearchResult>}
 */
proto.kwaipilot.search.CodeSearchResponse.prototype.getCodeSearchResultsList = function() {
  return /** @type{!Array<!proto.kwaipilot.search.CodeSearchResult>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.search.CodeSearchResult, 1));
};


/**
 * @param {!Array<!proto.kwaipilot.search.CodeSearchResult>} value
 * @return {!proto.kwaipilot.search.CodeSearchResponse} returns this
*/
proto.kwaipilot.search.CodeSearchResponse.prototype.setCodeSearchResultsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.kwaipilot.search.CodeSearchResult=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.search.CodeSearchResult}
 */
proto.kwaipilot.search.CodeSearchResponse.prototype.addCodeSearchResults = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.kwaipilot.search.CodeSearchResult, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.search.CodeSearchResponse} returns this
 */
proto.kwaipilot.search.CodeSearchResponse.prototype.clearCodeSearchResultsList = function() {
  return this.setCodeSearchResultsList([]);
};


/**
 * optional string prompt = 2;
 * @return {string}
 */
proto.kwaipilot.search.CodeSearchResponse.prototype.getPrompt = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.CodeSearchResponse} returns this
 */
proto.kwaipilot.search.CodeSearchResponse.prototype.setPrompt = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.UploadedFile.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.UploadedFile.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.UploadedFile} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.UploadedFile.toObject = function(includeInstance, msg) {
  var f, obj = {
    filename: jspb.Message.getFieldWithDefault(msg, 1, ""),
    fileIndex: jspb.Message.getFieldWithDefault(msg, 2, 0),
    pageNum: jspb.Message.getFieldWithDefault(msg, 3, 0),
    pageContent: jspb.Message.getFieldWithDefault(msg, 4, "")
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.UploadedFile}
 */
proto.kwaipilot.search.UploadedFile.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.UploadedFile;
  return proto.kwaipilot.search.UploadedFile.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.UploadedFile} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.UploadedFile}
 */
proto.kwaipilot.search.UploadedFile.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setFilename(value);
      break;
    case 2:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setFileIndex(value);
      break;
    case 3:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPageNum(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setPageContent(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.UploadedFile.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.UploadedFile.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.UploadedFile} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.UploadedFile.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getFilename();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getFileIndex();
  if (f !== 0) {
    writer.writeInt32(
      2,
      f
    );
  }
  f = message.getPageNum();
  if (f !== 0) {
    writer.writeInt32(
      3,
      f
    );
  }
  f = message.getPageContent();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
};


/**
 * optional string filename = 1;
 * @return {string}
 */
proto.kwaipilot.search.UploadedFile.prototype.getFilename = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.UploadedFile} returns this
 */
proto.kwaipilot.search.UploadedFile.prototype.setFilename = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional int32 file_index = 2;
 * @return {number}
 */
proto.kwaipilot.search.UploadedFile.prototype.getFileIndex = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 2, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.search.UploadedFile} returns this
 */
proto.kwaipilot.search.UploadedFile.prototype.setFileIndex = function(value) {
  return jspb.Message.setProto3IntField(this, 2, value);
};


/**
 * optional int32 page_num = 3;
 * @return {number}
 */
proto.kwaipilot.search.UploadedFile.prototype.getPageNum = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 3, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.search.UploadedFile} returns this
 */
proto.kwaipilot.search.UploadedFile.prototype.setPageNum = function(value) {
  return jspb.Message.setProto3IntField(this, 3, value);
};


/**
 * optional string page_content = 4;
 * @return {string}
 */
proto.kwaipilot.search.UploadedFile.prototype.getPageContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.UploadedFile} returns this
 */
proto.kwaipilot.search.UploadedFile.prototype.setPageContent = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.search.WebSearchRequest.repeatedFields_ = [2];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.WebSearchRequest.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.WebSearchRequest.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.WebSearchRequest} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.WebSearchRequest.toObject = function(includeInstance, msg) {
  var f, obj = {
    query: jspb.Message.getFieldWithDefault(msg, 1, ""),
    uploadedFilesList: jspb.Message.toObjectList(msg.getUploadedFilesList(),
    proto.kwaipilot.search.UploadedFile.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.WebSearchRequest}
 */
proto.kwaipilot.search.WebSearchRequest.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.WebSearchRequest;
  return proto.kwaipilot.search.WebSearchRequest.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.WebSearchRequest} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.WebSearchRequest}
 */
proto.kwaipilot.search.WebSearchRequest.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setQuery(value);
      break;
    case 2:
      var value = new proto.kwaipilot.search.UploadedFile;
      reader.readMessage(value,proto.kwaipilot.search.UploadedFile.deserializeBinaryFromReader);
      msg.addUploadedFiles(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.WebSearchRequest.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.WebSearchRequest.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.WebSearchRequest} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.WebSearchRequest.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getQuery();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getUploadedFilesList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      2,
      f,
      proto.kwaipilot.search.UploadedFile.serializeBinaryToWriter
    );
  }
};


/**
 * optional string query = 1;
 * @return {string}
 */
proto.kwaipilot.search.WebSearchRequest.prototype.getQuery = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.WebSearchRequest} returns this
 */
proto.kwaipilot.search.WebSearchRequest.prototype.setQuery = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * repeated UploadedFile uploaded_files = 2;
 * @return {!Array<!proto.kwaipilot.search.UploadedFile>}
 */
proto.kwaipilot.search.WebSearchRequest.prototype.getUploadedFilesList = function() {
  return /** @type{!Array<!proto.kwaipilot.search.UploadedFile>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.search.UploadedFile, 2));
};


/**
 * @param {!Array<!proto.kwaipilot.search.UploadedFile>} value
 * @return {!proto.kwaipilot.search.WebSearchRequest} returns this
*/
proto.kwaipilot.search.WebSearchRequest.prototype.setUploadedFilesList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 2, value);
};


/**
 * @param {!proto.kwaipilot.search.UploadedFile=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.search.UploadedFile}
 */
proto.kwaipilot.search.WebSearchRequest.prototype.addUploadedFiles = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 2, opt_value, proto.kwaipilot.search.UploadedFile, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.search.WebSearchRequest} returns this
 */
proto.kwaipilot.search.WebSearchRequest.prototype.clearUploadedFilesList = function() {
  return this.setUploadedFilesList([]);
};



/**
 * List of repeated fields within this message type.
 * @private {!Array<number>}
 * @const
 */
proto.kwaipilot.search.WebSearchResponse.repeatedFields_ = [1];



if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.WebSearchResponse.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.WebSearchResponse.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.WebSearchResponse} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.WebSearchResponse.toObject = function(includeInstance, msg) {
  var f, obj = {
    webSnippetsList: jspb.Message.toObjectList(msg.getWebSnippetsList(),
    proto.kwaipilot.search.WebSnippet.toObject, includeInstance)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.WebSearchResponse}
 */
proto.kwaipilot.search.WebSearchResponse.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.WebSearchResponse;
  return proto.kwaipilot.search.WebSearchResponse.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.WebSearchResponse} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.WebSearchResponse}
 */
proto.kwaipilot.search.WebSearchResponse.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = new proto.kwaipilot.search.WebSnippet;
      reader.readMessage(value,proto.kwaipilot.search.WebSnippet.deserializeBinaryFromReader);
      msg.addWebSnippets(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.WebSearchResponse.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.WebSearchResponse.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.WebSearchResponse} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.WebSearchResponse.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getWebSnippetsList();
  if (f.length > 0) {
    writer.writeRepeatedMessage(
      1,
      f,
      proto.kwaipilot.search.WebSnippet.serializeBinaryToWriter
    );
  }
};


/**
 * repeated WebSnippet web_snippets = 1;
 * @return {!Array<!proto.kwaipilot.search.WebSnippet>}
 */
proto.kwaipilot.search.WebSearchResponse.prototype.getWebSnippetsList = function() {
  return /** @type{!Array<!proto.kwaipilot.search.WebSnippet>} */ (
    jspb.Message.getRepeatedWrapperField(this, proto.kwaipilot.search.WebSnippet, 1));
};


/**
 * @param {!Array<!proto.kwaipilot.search.WebSnippet>} value
 * @return {!proto.kwaipilot.search.WebSearchResponse} returns this
*/
proto.kwaipilot.search.WebSearchResponse.prototype.setWebSnippetsList = function(value) {
  return jspb.Message.setRepeatedWrapperField(this, 1, value);
};


/**
 * @param {!proto.kwaipilot.search.WebSnippet=} opt_value
 * @param {number=} opt_index
 * @return {!proto.kwaipilot.search.WebSnippet}
 */
proto.kwaipilot.search.WebSearchResponse.prototype.addWebSnippets = function(opt_value, opt_index) {
  return jspb.Message.addToRepeatedWrapperField(this, 1, opt_value, proto.kwaipilot.search.WebSnippet, opt_index);
};


/**
 * Clears the list making it empty but non-null.
 * @return {!proto.kwaipilot.search.WebSearchResponse} returns this
 */
proto.kwaipilot.search.WebSearchResponse.prototype.clearWebSnippetsList = function() {
  return this.setWebSnippetsList([]);
};





if (jspb.Message.GENERATE_TO_OBJECT) {
/**
 * Creates an object representation of this proto.
 * Field names that are reserved in JavaScript and will be renamed to pb_name.
 * Optional fields that are not set will be set to undefined.
 * To access a reserved field use, foo.pb_<name>, eg, foo.pb_default.
 * For the list of reserved names please see:
 *     net/proto2/compiler/js/internal/generator.cc#kKeyword.
 * @param {boolean=} opt_includeInstance Deprecated. whether to include the
 *     JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @return {!Object}
 */
proto.kwaipilot.search.WebSnippet.prototype.toObject = function(opt_includeInstance) {
  return proto.kwaipilot.search.WebSnippet.toObject(opt_includeInstance, this);
};


/**
 * Static version of the {@see toObject} method.
 * @param {boolean|undefined} includeInstance Deprecated. Whether to include
 *     the JSPB instance for transitional soy proto support:
 *     http://goto/soy-param-migration
 * @param {!proto.kwaipilot.search.WebSnippet} msg The msg instance to transform.
 * @return {!Object}
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.WebSnippet.toObject = function(includeInstance, msg) {
  var f, obj = {
    title: jspb.Message.getFieldWithDefault(msg, 1, ""),
    link: jspb.Message.getFieldWithDefault(msg, 2, ""),
    date: jspb.Message.getFieldWithDefault(msg, 3, ""),
    favicon: jspb.Message.getFieldWithDefault(msg, 4, ""),
    content: jspb.Message.getFieldWithDefault(msg, 5, ""),
    prevContent: jspb.Message.getFieldWithDefault(msg, 6, ""),
    nextContent: jspb.Message.getFieldWithDefault(msg, 7, ""),
    fileIndex: jspb.Message.getFieldWithDefault(msg, 8, 0),
    pageNum: jspb.Message.getFieldWithDefault(msg, 9, 0)
  };

  if (includeInstance) {
    obj.$jspbMessageInstance = msg;
  }
  return obj;
};
}


/**
 * Deserializes binary data (in protobuf wire format).
 * @param {jspb.ByteSource} bytes The bytes to deserialize.
 * @return {!proto.kwaipilot.search.WebSnippet}
 */
proto.kwaipilot.search.WebSnippet.deserializeBinary = function(bytes) {
  var reader = new jspb.BinaryReader(bytes);
  var msg = new proto.kwaipilot.search.WebSnippet;
  return proto.kwaipilot.search.WebSnippet.deserializeBinaryFromReader(msg, reader);
};


/**
 * Deserializes binary data (in protobuf wire format) from the
 * given reader into the given message object.
 * @param {!proto.kwaipilot.search.WebSnippet} msg The message object to deserialize into.
 * @param {!jspb.BinaryReader} reader The BinaryReader to use.
 * @return {!proto.kwaipilot.search.WebSnippet}
 */
proto.kwaipilot.search.WebSnippet.deserializeBinaryFromReader = function(msg, reader) {
  while (reader.nextField()) {
    if (reader.isEndGroup()) {
      break;
    }
    var field = reader.getFieldNumber();
    switch (field) {
    case 1:
      var value = /** @type {string} */ (reader.readString());
      msg.setTitle(value);
      break;
    case 2:
      var value = /** @type {string} */ (reader.readString());
      msg.setLink(value);
      break;
    case 3:
      var value = /** @type {string} */ (reader.readString());
      msg.setDate(value);
      break;
    case 4:
      var value = /** @type {string} */ (reader.readString());
      msg.setFavicon(value);
      break;
    case 5:
      var value = /** @type {string} */ (reader.readString());
      msg.setContent(value);
      break;
    case 6:
      var value = /** @type {string} */ (reader.readString());
      msg.setPrevContent(value);
      break;
    case 7:
      var value = /** @type {string} */ (reader.readString());
      msg.setNextContent(value);
      break;
    case 8:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setFileIndex(value);
      break;
    case 9:
      var value = /** @type {number} */ (reader.readInt32());
      msg.setPageNum(value);
      break;
    default:
      reader.skipField();
      break;
    }
  }
  return msg;
};


/**
 * Serializes the message to binary data (in protobuf wire format).
 * @return {!Uint8Array}
 */
proto.kwaipilot.search.WebSnippet.prototype.serializeBinary = function() {
  var writer = new jspb.BinaryWriter();
  proto.kwaipilot.search.WebSnippet.serializeBinaryToWriter(this, writer);
  return writer.getResultBuffer();
};


/**
 * Serializes the given message to binary data (in protobuf wire
 * format), writing to the given BinaryWriter.
 * @param {!proto.kwaipilot.search.WebSnippet} message
 * @param {!jspb.BinaryWriter} writer
 * @suppress {unusedLocalVariables} f is only used for nested messages
 */
proto.kwaipilot.search.WebSnippet.serializeBinaryToWriter = function(message, writer) {
  var f = undefined;
  f = message.getTitle();
  if (f.length > 0) {
    writer.writeString(
      1,
      f
    );
  }
  f = message.getLink();
  if (f.length > 0) {
    writer.writeString(
      2,
      f
    );
  }
  f = message.getDate();
  if (f.length > 0) {
    writer.writeString(
      3,
      f
    );
  }
  f = message.getFavicon();
  if (f.length > 0) {
    writer.writeString(
      4,
      f
    );
  }
  f = message.getContent();
  if (f.length > 0) {
    writer.writeString(
      5,
      f
    );
  }
  f = message.getPrevContent();
  if (f.length > 0) {
    writer.writeString(
      6,
      f
    );
  }
  f = message.getNextContent();
  if (f.length > 0) {
    writer.writeString(
      7,
      f
    );
  }
  f = message.getFileIndex();
  if (f !== 0) {
    writer.writeInt32(
      8,
      f
    );
  }
  f = message.getPageNum();
  if (f !== 0) {
    writer.writeInt32(
      9,
      f
    );
  }
};


/**
 * optional string title = 1;
 * @return {string}
 */
proto.kwaipilot.search.WebSnippet.prototype.getTitle = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 1, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.WebSnippet} returns this
 */
proto.kwaipilot.search.WebSnippet.prototype.setTitle = function(value) {
  return jspb.Message.setProto3StringField(this, 1, value);
};


/**
 * optional string link = 2;
 * @return {string}
 */
proto.kwaipilot.search.WebSnippet.prototype.getLink = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 2, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.WebSnippet} returns this
 */
proto.kwaipilot.search.WebSnippet.prototype.setLink = function(value) {
  return jspb.Message.setProto3StringField(this, 2, value);
};


/**
 * optional string date = 3;
 * @return {string}
 */
proto.kwaipilot.search.WebSnippet.prototype.getDate = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 3, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.WebSnippet} returns this
 */
proto.kwaipilot.search.WebSnippet.prototype.setDate = function(value) {
  return jspb.Message.setProto3StringField(this, 3, value);
};


/**
 * optional string favicon = 4;
 * @return {string}
 */
proto.kwaipilot.search.WebSnippet.prototype.getFavicon = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 4, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.WebSnippet} returns this
 */
proto.kwaipilot.search.WebSnippet.prototype.setFavicon = function(value) {
  return jspb.Message.setProto3StringField(this, 4, value);
};


/**
 * optional string content = 5;
 * @return {string}
 */
proto.kwaipilot.search.WebSnippet.prototype.getContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 5, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.WebSnippet} returns this
 */
proto.kwaipilot.search.WebSnippet.prototype.setContent = function(value) {
  return jspb.Message.setProto3StringField(this, 5, value);
};


/**
 * optional string prev_content = 6;
 * @return {string}
 */
proto.kwaipilot.search.WebSnippet.prototype.getPrevContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 6, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.WebSnippet} returns this
 */
proto.kwaipilot.search.WebSnippet.prototype.setPrevContent = function(value) {
  return jspb.Message.setProto3StringField(this, 6, value);
};


/**
 * optional string next_content = 7;
 * @return {string}
 */
proto.kwaipilot.search.WebSnippet.prototype.getNextContent = function() {
  return /** @type {string} */ (jspb.Message.getFieldWithDefault(this, 7, ""));
};


/**
 * @param {string} value
 * @return {!proto.kwaipilot.search.WebSnippet} returns this
 */
proto.kwaipilot.search.WebSnippet.prototype.setNextContent = function(value) {
  return jspb.Message.setProto3StringField(this, 7, value);
};


/**
 * optional int32 file_index = 8;
 * @return {number}
 */
proto.kwaipilot.search.WebSnippet.prototype.getFileIndex = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 8, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.search.WebSnippet} returns this
 */
proto.kwaipilot.search.WebSnippet.prototype.setFileIndex = function(value) {
  return jspb.Message.setProto3IntField(this, 8, value);
};


/**
 * optional int32 page_num = 9;
 * @return {number}
 */
proto.kwaipilot.search.WebSnippet.prototype.getPageNum = function() {
  return /** @type {number} */ (jspb.Message.getFieldWithDefault(this, 9, 0));
};


/**
 * @param {number} value
 * @return {!proto.kwaipilot.search.WebSnippet} returns this
 */
proto.kwaipilot.search.WebSnippet.prototype.setPageNum = function(value) {
  return jspb.Message.setProto3IntField(this, 9, value);
};


goog.object.extend(exports, proto.kwaipilot.search);
