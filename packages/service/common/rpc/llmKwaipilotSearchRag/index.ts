import { RPC, GRPCProtocol, KessRegistry, Consumer } from '@infra-node/rpc';
import { IConsumer } from './proto/rag_grpc_pb';
import { IS_PREONLINE } from '@fastgpt/global/core/chat/constants';
import { createLogger } from '@fastgpt/global/common/util/logger';

const logger = createLogger('llm-service');
export const TIMEOUT = 20_000;

const GRPC_JAVA_SERVICE = IS_PREONLINE
  ? 'grpc_python_kwaipilot_search_rag_test'
  : 'grpc_python_kwaipilot_search_rag2';

logger.info(`current env: ${IS_PREONLINE ? 'pre' : 'prod'}, service: ${GRPC_JAVA_SERVICE}`);

const registry = new KessRegistry({
  balance: 'round-robin',
  serviceName: GRPC_JAVA_SERVICE
});

const protocol = new GRPCProtocol({
  methodLowerCamelCase: true,
  protoLoader: {
    enums: Number,
    oneofs: false
  },
  proto: require('./proto/rag_grpc_pb')
});

let consumer: Consumer<IConsumer> | null = null;
export async function getSearchRagServiceConsumer() {
  if (!consumer) {
    consumer = await RPC.createConsumer<IConsumer>({
      registry,
      protocol,
      timeout: TIMEOUT
    });
  }
  return consumer;
}

export * from './proto/rag_pb';
