import { IS_PROD } from '@fastgpt/global/core/chat/constants';
import { createKconf } from '@infra-node/kconf';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { LLM_CLUSTER, type LLMCluster } from '@fastgpt/global/common/constants';
import type { ProxyRegion } from '../proxy';
import type { GatewayAppInfo } from '@fastgpt/global/core/workflow/type';

const logger = createLogger('common-kconf');
const kconfEnv = IS_PROD ? 'prod' : 'staging';

logger.info(`init kconf ${kconfEnv} ${process.env.APP_ENV}`);
export const kconf = createKconf({
  env: kconfEnv
});

type APIConfig = {
  baseUrl: string;
  apiKey: string;
  proxy: ProxyRegion;
  queries?: Record<string, string>;
  headers?: Record<string, string>;
};

export const getOpenAIConfig = async (provider: 'azure' | 'other') => {
  const config = await kconf.getJSONValue<{
    azureList: APIConfig[];
    otherList: APIConfig[];
  }>('kwaipilot.server.workflow_api_config');

  return provider === 'azure' ? config.azureList : config.otherList;
};

export const getOverseaInternalApiWhitelist = async () => {
  const config = await kconf.getJSONValue<{
    rules: Array<{
      host: string;
      paths: string[];
    }>;
  }>('kwaipilot.platform.oversea_internal_api_whitelist');
  return config?.rules || [];
};

export type RagQuoteType = 'code' | 'file' | 'docs' | 'web';

export type QuoteType = RagQuoteType | 'simple';

type RagQuotePromptTemplates = {
  systemPrompt: string;
  userPrompt: string;
  noQuoteUserPrompt: string;
  toolsSystemPrompt?: string;
};

export const getRagSearchQuotePrompt = async (type: RagQuoteType) => {
  const config = await kconf.getJSONValue<Record<RagQuoteType, RagQuotePromptTemplates>>(
    'kwaipilot.server.rag_search_quote_prompt'
  );

  logger.info(`rag quotePrompt config: ${JSON.stringify(config)}`);
  const item = config[type];
  return {
    systemPrompt: item?.systemPrompt || '',
    userPrompt: item?.userPrompt || '',
    noQuoteUserPrompt: item?.noQuoteUserPrompt || ''
  };
};

export const getSimpleQuotePrompt = async () => {
  return await kconf.getStringValue('kwaipilot.server.quotePrompt');
};

// 获取当前网关应用，使用的集群
export async function getGatewayAppLLMCluster(appInfo: GatewayAppInfo): Promise<LLMCluster> {
  if (!appInfo || !appInfo.id) {
    logger.info('appInfo is empty');
    return LLM_CLUSTER.COMMON;
  }

  logger.info(`appId: ${appInfo.id}`);

  const appId = appInfo.id;
  const data = await kconf.getJSONValue<
    Record<
      string,
      {
        cluster?: string;
      }
    >
  >('dmo.aidevops.unlimit-whitelist');

  logger.info(`data: ${JSON.stringify(data)}`);

  const config = data[appId];
  if (!config || !config.cluster) {
    return LLM_CLUSTER.COMMON;
  }

  if (!Object.values(LLM_CLUSTER).includes(config.cluster as LLMCluster)) {
    return LLM_CLUSTER.COMMON;
  }

  return config.cluster as LLMCluster;
}

export async function getRagWebSearchToolSystemPrompt() {
  const config = await kconf.getJSONValue<Record<RagQuoteType, RagQuotePromptTemplates>>(
    'kwaipilot.server.rag_search_quote_prompt'
  );
  const item = config['web'];
  return item?.toolsSystemPrompt || '';
}
