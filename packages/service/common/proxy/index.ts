import { kconf } from '../kconf';
import { createLogger } from '@fastgpt/global/common/util/logger';

export type ProxyRegion = 'china' | 'foreign' | 'foreign-kix';

const logger = createLogger('network-proxy');

async function getProxies(region: ProxyRegion) {
  if (process.env.CLOUDDEV_CONTAINER !== '1' && !process.env.KWS_SERVICE_NAME) {
    return [];
  }

  if (process.env.CLOUDDEV_CONTAINER === '1') {
    return region === 'china' ? ['************:11080'] : ['***********:11080'];
  }

  try {
    const map = await kconf.getJSONValue<Record<string, Array<string>>>(
      'public.httpProxy.CommonProxiesList'
    );
    logger.info(`get proxy result ${JSON.stringify(map)}`);
    const array = map[region];
    return array || [];
  } catch (err) {
    logger.error('kconf error:', err);
    return [];
  }
}

export async function getOneProxy(region: ProxyRegion, reqUrl = '') {
  try {
    const hostname = new URL(reqUrl).hostname;
    if (
      hostname.endsWith('.corp.kuaishou.com') ||
      hostname.endsWith('.staging.kuaishou.com') ||
      hostname.endsWith('.test.gifshow.com') ||
      hostname.endsWith('.internal')
    ) {
      logger.info(`skip internal url ${reqUrl}`);
      return null;
    }
  } catch {}

  const array = await getProxies(region);
  const url = array.length ? array[Math.floor(Math.random() * array.length)] : '';
  logger.info(`proxy chosen ${region} ${url}`);
  if (url) {
    const seg = url.split(':');
    return {
      host: seg[0],
      port: seg[1]
    };
  }
  return null;
}