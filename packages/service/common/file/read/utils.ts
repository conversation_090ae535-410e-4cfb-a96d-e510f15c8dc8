import { markdownProcess, simpleMarkdownText } from '@fastgpt/global/common/string/markdown';
import { uploadMongoImg } from '../image/controller';
import { MongoImageTypeEnum } from '@fastgpt/global/common/file/image/constants';
import { addHours } from 'date-fns';
import axios from 'axios';
import { CachedFileResult, FilesCache } from '@fastgpt/global/common/cache';

import { WorkerNameEnum, runWorker } from '../../../worker/utils';
import { ReadFileResponse } from '../../../worker/file/type';
import { UserChatFile, UserChatFileWithoutContent } from '@fastgpt/global/core/chat/type';
import { ChatFileTypeEnum } from '@fastgpt/global/core/chat/constants';
import { DOWNLOADED_FILE_COUNT_LIMIT, MAX_DOWNLOADED_FILE_SIZE } from '../constants';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { installDNSCache } from '@fastgpt/global/common/dns';

installDNSCache();
type WithIndex<T> = T & {
  index: number;
};

const logger = createLogger('file-utils');

export const initMarkdownText = ({
  teamId,
  md,
  metadata
}: {
  md: string;
  teamId: string;
  metadata?: Record<string, any>;
}) =>
  markdownProcess({
    rawText: md,
    uploadImgController: (base64Img) =>
      uploadMongoImg({
        type: MongoImageTypeEnum.collectionImage,
        base64Img,
        teamId,
        metadata,
        expiredTime: addHours(new Date(), 2)
      })
  });

export const readFileRawContent = async ({
  extension,
  csvFormat,
  teamId,
  buffer,
  encoding,
  metadata
}: {
  csvFormat?: boolean;
  extension: string;
  teamId: string;
  buffer: Buffer;
  encoding: string;
  metadata?: Record<string, any>;
}) => {
  const result = await runWorker<ReadFileResponse>(WorkerNameEnum.readFile, {
    extension,
    csvFormat,
    encoding,
    buffer
  });

  // markdown data format
  if (['md', 'html', 'docx'].includes(extension)) {
    result.rawText = await initMarkdownText({
      teamId: teamId,
      md: result.rawText,
      metadata: metadata
    });
  }

  return result;
};

export const htmlToMarkdown = async (html?: string | null) => {
  const md = await runWorker<string>(WorkerNameEnum.htmlStr2Md, { html: html || '' });

  return simpleMarkdownText(md);
};

/**
 * 获取文件大小
 * @param {string} url - 要下载的文件的 URL
 * @returns {Promise<number>} - 返回文件大小（字节）
 */
async function getFileSize(url: string) {
  try {
    const response = await axios.head(url, {
      timeout: 5000
    });
    return parseInt(response.headers['content-length'], 10);
  } catch (error) {
    logger.error(`Error fetching file size: ${JSON.stringify(error)}`, { url });
    return 0;
  }
}

const downloadAndParseFile = async (
  fileUrl: string,
  extension: string
): Promise<CachedFileResult> => {
  try {
    const cached = FilesCache.get(fileUrl);
    if (cached) {
      return cached;
    }

    const res = await axios({
      method: 'GET',
      url: fileUrl,
      responseType: 'arraybuffer',
      timeout: 30000
    });

    const buffer = res.data as Buffer;

    const fileRes = await readFileRawContent({
      extension,
      csvFormat: extension === 'csv',
      teamId: '',
      buffer,
      encoding: 'utf-8'
    });

    const text = fileRes.rawText || '';
    const result: CachedFileResult = {
      rawText: text,
      pages: fileRes.pages
    };

    if (result.rawText) {
      FilesCache.set(fileUrl, result);
    }
    return result;
  } catch (error) {
    logger.error(`Error downloading and parsing file: ${JSON.stringify(error)}`, {
      fileUrl,
      extension,
      error
    });
    return {
      rawText: '',
      pages: []
    };
  }
};

type FileToBeParsed = WithIndex<{
  url: string;
  name: string;
  type: `${ChatFileTypeEnum}`;
  size: number;
}>;

type ParsedFile = WithIndex<
  UserChatFile & {
    pages: Array<{
      pageNumber: number;
      content: string;
    }>;
  }
>;

export const getAvailableInputFiles = async (
  originalFiles: UserChatFileWithoutContent[]
): Promise<ParsedFile[]> => {
  if (originalFiles.length === 0) {
    return [];
  }

  // 去重
  const files: Array<WithIndex<UserChatFileWithoutContent>> = [];
  originalFiles.forEach((o, idx) => {
    if (files.find((f) => f.url === o.url)) {
      return;
    }

    // 图片不需要下载
    if (o.type === ChatFileTypeEnum.image) {
      return;
    }

    files.push({
      ...o,
      index: idx
    });
  });

  // 先获取所有文件的大小
  const candidateTextFilePromises: Array<Promise<FileToBeParsed>> = [];

  const prepareFileSize = async (f: Omit<FileToBeParsed, 'size'>) => {
    const size = await getFileSize(f.url);
    return {
      ...f,
      size
    };
  };

  for (let f of files) {
    // 不能超过文件数量限制
    if (candidateTextFilePromises.length >= DOWNLOADED_FILE_COUNT_LIMIT) {
      break;
    }

    // 获取文件大小
    if (f.type === ChatFileTypeEnum.file) {
      candidateTextFilePromises.push(
        prepareFileSize({
          type: f.type,
          url: f.url,
          name: f.name,
          index: f.index
        })
      );
    }
  }

  const candidateTextFiles = await Promise.all(candidateTextFilePromises);
  // 过滤掉超过大小限制的文件
  const filesToBeParsed: ParsedFile[] = [];
  let totalSize = 0;
  for await (let f of candidateTextFiles) {
    if (f.size === 0) {
      filesToBeParsed.push({
        ...f,
        content: '',
        pages: []
      });
      continue;
    }

    totalSize += f.size;
    // 不能超过文件大小限制
    if (totalSize > MAX_DOWNLOADED_FILE_SIZE) {
      break;
    }

    const extension = f.name.split('.').pop();
    if (!extension) {
      continue;
    }
    const { rawText, pages } = await downloadAndParseFile(f.url, extension);
    filesToBeParsed.push({
      ...f,
      content: rawText,
      pages: pages ?? []
    });
  }

  return filesToBeParsed;
};
