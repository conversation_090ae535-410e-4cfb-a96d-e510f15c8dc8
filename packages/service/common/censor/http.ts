import { Kafka } from '@infra-node/kafka';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { IS_PROD, CENSOR_DATA_KAFKA_TOPIC } from '@fastgpt/global/core/chat/constants';

const kafka = new Kafka({
  logicTopic: CENSOR_DATA_KAFKA_TOPIC,
  env: IS_PROD ? undefined : 'staging'
});

const producer = kafka.producer();
const logger = createLogger('censor-http');

const connected = producer.connect().then(() => producer);

export type CensorData = {
  requestedAt: number;
  username: string;
  appId: string;
  url: string;
  headers: Record<string, string>;
  method: string;
  body: Record<string, any>;
  type: 'http' | 'legacy-tool' | 'tool';
};

export async function censorHttpMessage(data: CensorData) {
  try {
    const info = {
      type: `agent-${data.type}`,
      requestAt: data.requestedAt,
      username: data.username,
      appId: data.appId,
      data: JSON.stringify(data)
    };
    logger.info(`${data.method} ${data.url} ${data.appId}`, info);

    await connected;
    producer.send({
      messages: [{ value: JSON.stringify(info) }]
    });
  } catch (e: unknown) {
    logger.error(
      `${data.method} ${data.url} ${data.appId} ${e instanceof Error ? e.message : String(e)}`,
      e
    );
  }
}
