import { Kafka } from '@infra-node/kafka';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { ChatCompletionMessageToolCall } from 'openai/resources';
import { ChatCompletionMessageFunctionCall } from '@fastgpt/global/core/ai/type';
import { IS_PROD, LLM_CALLING_KAFKA_TOPIC } from '@fastgpt/global/core/chat/constants';

const kafka = new Kafka({
  logicTopic: LLM_CALLING_KAFKA_TOPIC,
  env: IS_PROD ? undefined : 'staging'
});

const producer = kafka.producer();
const logger = createLogger('produce-completion-message');

const connected = producer.connect().then(() => producer);

export interface CensorData {
  requestedAt: number;
  responseStart: number;
  userId: string;
  provider: 'openai';
  inputParams: unknown;
  finalAnswer: string;
  appInfo: {
    tokenName: string;
    tokenOwner: string;
    id: string;
  };
  model: string;
  calls?: ChatCompletionMessageToolCall[];
  functions?: ChatCompletionMessageFunctionCall[];
}

export async function censorLLMMessage({
  requestedAt,
  userId,
  responseStart,
  provider,
  inputParams,
  finalAnswer,
  model,
  appInfo,
  calls = [],
  functions = []
}: CensorData) {
  // 没有任何生成数据
  if (!finalAnswer && calls.length === 0 && functions.length === 0) {
    return;
  }
  const now = new Date();
  const year = now.getFullYear();
  const month = now.getMonth() + 1;
  const day = now.getDate();
  const date = `${year}${month < 10 ? '0' : ''}${month}${day < 10 ? '0' : ''}${day}`;
  const info = {
    userId,
    // 请求进入时间
    requestedAt,
    responseStart,
    responseEnd: Date.now(),
    rawBody: JSON.stringify(inputParams),
    provider,
    model,
    // 日期
    date,
    // 全量
    completionDetail: finalAnswer,
    completionMessage: finalAnswer,
    appId: appInfo.id,
    // token
    tokenName: appInfo.tokenName,
    tokenOwner: appInfo.tokenOwner,
    useExternalOpenai: false,
    source: 20,
    toolCalls: calls,
    functionCalls: functions
  };
  logger.info(`${userId} ${model} ${appInfo.id}`, info);

  await connected;
  producer.send({
    messages: [{ value: JSON.stringify(info) }]
  });
}
