import { IS_PREONLINE, IS_PROD, IS_OVERSEA } from '@fastgpt/global/core/chat/constants';
import { post } from './base';

const RAG_WEB_URL = IS_PREONLINE
  ? 'http://kwaipilot-tool.internal/node/api/search/rag'
  : IS_PROD
    ? 'http://kwaipilot-tool.internal/node/api/search/rag'
    : 'https://kwaipilot-tool.corp.kuaishou.com/node/api/search/rag';

const RAG_WEB_URL_OVERSEA = IS_PROD
  ? 'http://kwaipilot-server.internal/papi/rag/web'
  : 'https://qa-keyes.corp.kuaishou.com/papi/rag/web';

const REQ_URL = IS_OVERSEA ? RAG_WEB_URL_OVERSEA : RAG_WEB_URL;
export type RagSearchFile = {
  filename: string;
  fileIndex: number;
  pageNumber: number;
  content: string;
};

export type RagWebSearchItem = {
  title: string;
  link: string;
  date: string;
  favicon: string;
  content: string;
  prevContent: string;
  nextContent: string;
  fileIndex: number;
  pageNum: number;
};

export type RAGSearchToolResponse = {
  status: 'success' | 'error';
  message: string;
  results: RagWebSearchItem[];
};

export async function ragSearch(
  query: string,
  files: RagSearchFile[]
): Promise<RAGSearchToolResponse> {
  if (IS_OVERSEA) {
    return post<RAGSearchToolResponse>(REQ_URL, {
      body: {
        query,
        uploadedFiles: files.map((file) => ({
          filename: file.filename,
          fileIndex: file.fileIndex,
          pageNum: file.pageNumber,
          pageContent: file.content
        }))
      }
    });
  }
  return post<RAGSearchToolResponse>(REQ_URL, {
    body: {
      query,
      files
    }
  });
}
