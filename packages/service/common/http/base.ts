import axios from 'axios';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { installDNSCache } from '@fastgpt/global/common/dns';

installDNSCache();
const logger = createLogger('http-base');
const TIMEOUT = 120000;

export async function get<T = unknown>(
  url: string,
  config: {
    params?: Record<string, string>;
    headers?: Record<string, string>;
  }
): Promise<T> {
  try {
    const { data } = await axios<T>({
      method: 'GET',
      url,
      headers: {
        'Content-Type': 'application/json',
        ...(config.headers ?? {})
      },
      params: config.params,
      timeout: TIMEOUT
    });
    logger.info(`get ${url} success ${JSON.stringify(data)}`);
    return data;
  } catch (error) {
    logger.error(`get ${url} error: ${error}`);
    throw error;
  }
}

export async function post<T = unknown, U = unknown>(
  url: string,
  config: {
    params?: Record<string, string>;
    headers?: Record<string, string>;
    body: U;
  }
): Promise<T> {
  try {
    const { data } = await axios<T>({
      method: 'POST',
      url,
      headers: {
        'Content-Type': 'application/json',
        ...(config.headers ?? {})
      },
      params: config.params,
      data: config.body,
      timeout: TIMEOUT
    });

    logger.info(`post ${url} success ${JSON.stringify(data)}`);
    return data;
  } catch (error) {
    logger.error(`post ${url} error: ${error}`);
    throw error;
  }
}
