import { IMG_BLOCK_KEY } from '@fastgpt/global/core/chat/constants';
import { countGptMessagesTokens } from '../../common/string/tiktoken/index';
import type {
  ChatCompletionContentPart,
  ChatCompletionMessageParam
} from '@fastgpt/global/core/ai/type.d';
import axios from 'axios';
import { ChatCompletionRequestMessageRoleEnum } from '@fastgpt/global/core/ai/constants';
import { getImageBase64Url } from '../../common/file/image';
import { createLogger } from '@fastgpt/global/common/util/logger';

const logger = createLogger('core-chat-utils');

const splitChatsToGroups = (messages: ChatCompletionMessageParam[]) => {
  let question: ChatCompletionMessageParam | undefined = undefined;
  const systemPrompts: ChatCompletionMessageParam[] = messages.filter(
    (item) => item.role === ChatCompletionRequestMessageRoleEnum.System
  );
  const chatPrompts: ChatCompletionMessageParam[] = messages.filter(
    (item) => item.role !== ChatCompletionRequestMessageRoleEnum.System
  );

  // 开始截取消息对。消息对必须以 user role 开头，如果碰到 user role 消息，则把之前的消息都截取出来加入消息组
  let pendingGroup: ChatCompletionMessageParam[] = [];
  const messageGroups: ChatCompletionMessageParam[][] = [];
  for (let i = chatPrompts.length - 1; i >= 0; i--) {
    const currentMessage = chatPrompts[i];
    // 如果是用户消息，将当前消息组添加到组列表中
    if (currentMessage.role === ChatCompletionRequestMessageRoleEnum.User) {
      // 只有打头的是 user 消息，才会把当前消息作为 question
      if (
        typeof question === 'undefined' &&
        messageGroups.length === 0 &&
        pendingGroup.length === 0
      ) {
        question = currentMessage;
        continue;
      }

      // 如果当前消息组不为空，则将当前消息组添加到组列表中
      if (pendingGroup.length > 0) {
        messageGroups.unshift([currentMessage, ...pendingGroup]);
        pendingGroup = [];
      }
    }
    // 如果不是用户消息，添加到当前消息组
    // 如果还有剩余的消息组未处理，说明最开始的不是用户消息，则忽略这些消息
    else {
      pendingGroup.unshift(currentMessage);
    }
  }

  if (pendingGroup.length > 0) {
    logger.warn(`splitChatsToGroups pendingGroup length ${pendingGroup.length}`, pendingGroup);
  }

  return {
    question,
    systemPrompts,
    messageGroups
  };
};

/* slice chat context by tokens */
const filterEmptyMessages = (messages: ChatCompletionMessageParam[]) => {
  const { systemPrompts, messageGroups, question } = splitChatsToGroups(messages);

  const groups = messageGroups.filter((g) => {
    const userValid = g.some(
      (item) => item.role === ChatCompletionRequestMessageRoleEnum.User && !!item.content
    );
    const assistantValid = g.some(
      (item) =>
        item.role === ChatCompletionRequestMessageRoleEnum.Assistant &&
        (!!item.content || !!item.function_call || !!item.tool_calls)
    );
    const toolValid = g.some(
      (item) => item.role === ChatCompletionRequestMessageRoleEnum.Tool && !!item.content
    );
    const functionValid = g.some(
      (item) => item.role === ChatCompletionRequestMessageRoleEnum.Function && !!item.content
    );
    // 如果存在有意义的 user message 和非 user message，则保留该组
    return userValid && (assistantValid || toolValid || functionValid);
  });

  return [...systemPrompts, ...groups.flat(), ...(question ? [question] : [])];
};

export const filterGPTMessageByMaxTokens = async ({
  messages = [],
  maxTokens
}: {
  messages: ChatCompletionMessageParam[];
  maxTokens: number;
}) => {
  logger.info(`filterGPTMessageByMaxTokens messages length ${messages.length}`, messages);

  if (!Array.isArray(messages)) {
    return [];
  }
  const rawTextLen = messages.reduce((sum, item) => {
    if (typeof item.content === 'string') {
      return sum + item.content.length;
    }
    if (Array.isArray(item.content)) {
      return (
        sum +
        item.content.reduce((sum, item) => {
          if (item.type === 'text') {
            return sum + item.text.length;
          }
          return sum;
        }, 0)
      );
    }
    return sum;
  }, 0);

  // If the text length is less than half of the maximum token, no calculation is required
  if (rawTextLen < maxTokens * 0.5) {
    const filteredMessages = filterEmptyMessages(messages);
    logger.info(
      `filterGPTMessageByMaxTokens filteredMessages1 length ${filteredMessages.length}`,
      filteredMessages
    );
    return filteredMessages;
  }

  // filter startWith system prompt
  const { systemPrompts, messageGroups, question } = splitChatsToGroups(messages);

  // reduce token of systemPrompt
  maxTokens -= await countGptMessagesTokens(systemPrompts);

  const chats: ChatCompletionMessageParam[] = [];
  if (question) {
    // reduce token of user input
    maxTokens -= await countGptMessagesTokens([question]);
    chats.unshift(question);
  }
  // 如果 question 为空，说明第一个 user 分到了 messageGroups 中了，需要保留
  else {
    const firstUserMessageGroup = messageGroups.pop();
    if (firstUserMessageGroup) {
      chats.unshift(...firstUserMessageGroup);
    }
  }

  // 从消息组中逐个添加消息，直到达到token限制
  while (1) {
    const group = messageGroups.pop();
    if (!group) {
      break;
    }

    const tokens = await countGptMessagesTokens(group);
    maxTokens -= tokens;
    /* 整体 tokens 超出范围，截断  */
    if (maxTokens < 0) {
      break;
    }

    chats.unshift(...group);

    if (messageGroups.length === 0) {
      break;
    }
  }

  const filteredMessages = filterEmptyMessages([...systemPrompts, ...chats]);
  logger.info(
    `filterGPTMessageByMaxTokens filteredMessages2 length ${filteredMessages.length}`,
    filteredMessages
  );
  return filteredMessages;
};

export const formatGPTMessagesInRequestBefore = (messages: ChatCompletionMessageParam[]) => {
  return messages
    .map((item) => {
      if (!item.content) return;
      if (typeof item.content === 'string') {
        return {
          ...item,
          content: item.content.trim()
        };
      }

      // array
      if (item.content.length === 0) return;
      if (item.content.length === 1 && item.content[0].type === 'text') {
        return {
          ...item,
          content: item.content[0].text
        };
      }

      return item;
    })
    .filter(Boolean) as ChatCompletionMessageParam[];
};

/**
    string to vision model. Follow the markdown code block rule for interception:

    @rule:
    ```img-block
        {src:""}
        {src:""}
    ```
    ```file-block
        {name:"",src:""},
        {name:"",src:""}
    ```
    @example:
        What’s in this image?
        ```img-block
            {src:"https://1.png"}
        ```
    @return 
        [
            { type: 'text', text: 'What’s in this image?' },
            {
              type: 'image_url',
              image_url: {
                url: 'https://1.png'
              }
            }
        ]
 */
export async function formatStr2ChatContent(str: string) {
  const content: ChatCompletionContentPart[] = [];
  let lastIndex = 0;
  const regex = new RegExp(`\`\`\`(${IMG_BLOCK_KEY})\\n([\\s\\S]*?)\`\`\``, 'g');

  const imgKey: 'image_url' = 'image_url';

  let match;

  while ((match = regex.exec(str)) !== null) {
    // add previous text
    if (match.index > lastIndex) {
      const text = str.substring(lastIndex, match.index).trim();
      if (text) {
        content.push({ type: 'text', text });
      }
    }

    const blockType = match[1].trim();

    if (blockType === IMG_BLOCK_KEY) {
      const blockContentLines = match[2].trim().split('\n');
      const jsonLines = blockContentLines.map((item) => {
        try {
          return JSON.parse(item) as { src: string };
        } catch (error) {
          return { src: '' };
        }
      });

      for (const item of jsonLines) {
        if (!item.src) throw new Error("image block's content error");
      }

      content.push(
        ...jsonLines.map((item) => ({
          type: imgKey,
          image_url: {
            url: item.src
          }
        }))
      );
    }

    lastIndex = regex.lastIndex;
  }

  // add remaining text
  if (lastIndex < str.length) {
    const remainingText = str.substring(lastIndex).trim();
    if (remainingText) {
      content.push({ type: 'text', text: remainingText });
    }
  }

  // Continuous text type content, if type=text, merge them
  for (let i = 0; i < content.length - 1; i++) {
    const currentContent = content[i];
    const nextContent = content[i + 1];
    if (currentContent.type === 'text' && nextContent.type === 'text') {
      currentContent.text += nextContent.text;
      content.splice(i + 1, 1);
      i--;
    }
  }

  if (content.length === 1 && content[0].type === 'text') {
    return content[0].text;
  }

  if (!content) return null;
  // load img to base64
  for await (const item of content) {
    if (item.type === imgKey && item[imgKey]?.url) {
      const response = await axios.get(item[imgKey].url, {
        responseType: 'arraybuffer'
      });
      const base64 = Buffer.from(response.data).toString('base64');
      item[imgKey].url = `data:${response.headers['content-type']};base64,${base64}`;
    }
  }

  return content ? content : null;
}

export const loadChatImgToBase64 = async (content: string | ChatCompletionContentPart[]) => {
  if (typeof content === 'string') {
    return content;
  }
  return Promise.all(
    content.map(async (item) => {
      if (item.type === 'text') return item;
      // load image
      item.image_url.url = await getImageBase64Url(item.image_url.url);
      return item;
    })
  );
};
