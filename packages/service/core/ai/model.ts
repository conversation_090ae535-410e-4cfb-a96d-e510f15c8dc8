import {
  getLLMServiceConsumer,
  ChatModelListQuery
} from '../../common/rpc/llmKwaipilotService/index';
import { LLMModelItemType, ModelConfig } from '@fastgpt/global/core/ai/model.d';
import { createLogger } from '@fastgpt/global/common/util/logger';
import { getModelConfig } from '@fastgpt/global/core/chat/utils';

const logger = createLogger('ai-model');

export const getRemoteLLMModel = async (
  model?: string,
  username?: string
): Promise<LLMModelItemType> => {
  if (!!process.env.DEBUG_MODEL_LIST) {
    return getLLMModel();
  }

  logger.info(`get remote llm consumer ${model}`);
  const consumer = await getLLMServiceConsumer();

  logger.info('get remote llm service');
  const service = await consumer.getPromisifyService('LlmServerService');

  const req = new ChatModelListQuery();

  if (username) {
    req.setUsername(username);
  }

  logger.info('send request to remote llm service');
  const res = await service.listChatModels(req);
  logger.info(`${model} ${username} rpc res`, res.toObject());

  const list = res.getChatModelsList().map((it) => {
    const isKwaipilot = it.getCode().startsWith('kwaipilot');
    const model = it.getCode();
    const isGPT = it.getCode().toLowerCase().startsWith('gpt');
    const config: ModelConfig = getModelConfig(model);
    return {
      model: it.getCode(),
      name: it.getName(),
      protocol: isKwaipilot ? 'rpc' : 'http',
      avatar: it.getIcon(),
      maxContext: config.maxContext,
      maxResponse: config.maxResponse,
      quoteMaxToken: config.maxResponse,
      supportImg: config.supportImg,
      maxTemperature: 1,
      charsPointsPrice: 0,
      vision: false,
      usedInClassify: true,
      usedInExtractFields: true,
      usedInToolCall: true,
      usedInQueryExtension: true,
      functionCall: false,
      // GPT 和 kwaipilot 32k 都支持 tool call
      toolChoice: isKwaipilot || isGPT,
      customCQPrompt: '',
      customExtractPrompt: '',
      defaultSystemChatPrompt: '',
      defaultConfig: {},
      censor: false
    };
  });
  logger.info(`${model} ${username} list`, list);

  if (!list.length) {
    throw new Error('没有可用模型');
  }

  if (model) {
    const ret = list.find((it) => it.model === model);
    if (!ret) {
      throw new Error(`暂不支持使用 ${model} 模型`);
    }
    return ret;
  }
  return list[0];
};

export const getLLMModel = (model?: string): LLMModelItemType => {
  // FIXME: zhouhongxuan 从接口获取
  return [
    {
      model: 'kwaipilot_pro_32k',
      name: 'kwaipilot-pro-32k',
      protocol: 'rpc',
      avatar: 'https://bs3-hb1.corp.kuaishou.com/upload-kinsight/kwaipilot/kwai_pilot_pro_32k.svg',
      maxContext: 50000,
      maxResponse: 50000,
      quoteMaxToken: 50000,
      maxTemperature: 1,
      charsPointsPrice: 0,
      vision: false,
      usedInClassify: true,
      usedInExtractFields: true,
      usedInToolCall: true,
      usedInQueryExtension: true,
      functionCall: true,
      toolChoice: true,
      customCQPrompt: '',
      customExtractPrompt: '',
      defaultSystemChatPrompt: '',
      defaultConfig: {},
      censor: false
    }
  ][0];
  // return global.llmModels.find((item) => item.model === model) ?? global.llmModels[0];
};
export const getDatasetModel = (model?: string) => {
  return (
    global.llmModels?.filter((item) => item.datasetProcess)?.find((item) => item.model === model) ??
    global.llmModels[0]
  );
};

export const getVectorModel = (model?: string) => {
  return global.vectorModels.find((item) => item.model === model) || global.vectorModels[0];
};

export function getAudioSpeechModel(model?: string) {
  return (
    global.audioSpeechModels.find((item) => item.model === model) || global.audioSpeechModels[0]
  );
}

export function getWhisperModel(model?: string) {
  return global.whisperModel;
}

export function getReRankModel(model?: string) {
  return global.reRankModels.find((item) => item.model === model);
}

export enum ModelTypeEnum {
  llm = 'llm',
  vector = 'vector',
  audioSpeech = 'audioSpeech',
  whisper = 'whisper',
  rerank = 'rerank'
}
export const getModelMap = {
  [ModelTypeEnum.llm]: getRemoteLLMModel,
  [ModelTypeEnum.vector]: getVectorModel,
  [ModelTypeEnum.audioSpeech]: getAudioSpeechModel,
  [ModelTypeEnum.whisper]: getWhisperModel,
  [ModelTypeEnum.rerank]: getReRankModel
};
