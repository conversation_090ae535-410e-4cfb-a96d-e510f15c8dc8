{"name": "@fastgpt/global", "version": "1.0.0", "dependencies": {"@apidevtools/swagger-parser": "^10.1.0", "@fortaine/fetch-event-source": "^3.0.6", "@infra-node/logger": "^1.1.17", "ajv": "^8.17.1", "axios": "^1.5.1", "cacheable-lookup": "^7.0.0", "cron-parser": "^4.9.0", "dayjs": "^1.11.7", "encoding": "^0.1.13", "js-yaml": "^4.1.0", "jschardet": "3.1.1", "lru-cache": "^11.0.0", "nanoid": "^4.0.1", "next": "13.5.2", "openai": "4.28.0", "openapi-types": "^12.1.3", "timezones-list": "^3.0.2", "winston": "^3.13.1", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/js-yaml": "^4.0.9", "@types/node": "^20.8.5"}}