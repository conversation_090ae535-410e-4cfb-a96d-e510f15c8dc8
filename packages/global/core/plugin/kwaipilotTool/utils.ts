import { isValidJSONSchema, JSONSchemaType } from '../../../common/util/json-schema';

export enum KwaipilotToolInputRawTypeLocation {
  Header = 'Header',
  Body = 'Body',
  Path = 'Path',
  Query = 'Query'
}

export type KwaipilotToolInputRawType = {
  name: string;
  location: KwaipilotToolInputRawTypeLocation;
  description: string;
  required?: boolean;
  type: string;
  default?: string;
};

export type KwaipilotToolInputSchemas = Record<
  KwaipilotToolInputRawTypeLocation,
  string | undefined
>;

export enum KwaipilotToolSchemaParseError {
  InvalidJSONSchema = 'InvalidJSONSchema',
  InvalidDefinition = 'InvalidDefinition'
}

type ValueGenerator = (
  schema: JSONSchemaType<any>,
  name: string,
  parentSchema: JSONSchemaType<any> | null
) => void;

export const getToolRawTypeFromShallowSchema = ({
  location,
  schemaStr
}: {
  location: KwaipilotToolInputRawTypeLocation;
  schemaStr?: string;
}): {
  types: KwaipilotToolInputRawType[];
  error?: KwaipilotToolSchemaParseError;
} => {
  if (!schemaStr) {
    return {
      types: []
    };
  }

  const schema = isValidJSONSchema<Record<string, string>>(schemaStr);
  if (!schema) {
    return {
      types: [],
      error: KwaipilotToolSchemaParseError.InvalidJSONSchema
    };
  }

  if (schema.type !== 'object' || !schema.properties) {
    return {
      types: [],
      error: KwaipilotToolSchemaParseError.InvalidDefinition
    };
  }

  const rawTypes: KwaipilotToolInputRawType[] = [];
  Object.entries(schema.properties as JSONSchemaType<unknown>[]).forEach(([name, s]) => {
    const required = schema.required?.includes(name) ?? s.required ?? false;
    rawTypes.push({
      name,
      location,
      type: String(s.type).toLowerCase(),
      required,
      description: s.description ?? name,
      default: s.default
    });
  });

  return {
    types: rawTypes
  };
};

const generateFromSchema = (
  name: string,
  schema: JSONSchemaType<unknown>,
  parentSchema: JSONSchemaType<unknown> | null,
  valGenerator: ValueGenerator
): unknown => {
  const generate = () => {
    if (schema.type === 'object') {
      const obj: Record<string, unknown> = {};
      for (const key in schema.properties) {
        obj[key] = generateFromSchema(key, schema.properties[key], schema, valGenerator);
      }
      return obj;
    }

    if (
      schema.type === 'string' ||
      schema.type === 'number' ||
      schema.type === 'integer' ||
      schema.type === 'boolean' ||
      schema.type === 'array'
    ) {
      return valGenerator(schema, name, parentSchema);
    }

    // Add more cases as needed for other types
    return null;
  };

  return generate();
};

const getBodyTypesFromSchema = ({
  valGenerator,
  schemaStr
}: {
  valGenerator: ValueGenerator;
  schemaStr?: string;
}): {
  types: string;
  error?: KwaipilotToolSchemaParseError;
} => {
  if (!schemaStr) {
    return {
      types: ''
    };
  }

  const schema = isValidJSONSchema<unknown>(schemaStr);
  if (!schema) {
    return {
      types: '',
      error: KwaipilotToolSchemaParseError.InvalidJSONSchema
    };
  }

  if (schema.type !== 'object' || !schema.properties) {
    return {
      types: '',
      error: KwaipilotToolSchemaParseError.InvalidDefinition
    };
  }
  const body = generateFromSchema('$bodyRoot', schema, null, valGenerator);

  return {
    types: JSON.stringify(body)
  };
};

export const getTypesFromSchema = ({
  schemas,
  valGenerator,
  onError
}: {
  schemas?: KwaipilotToolInputSchemas;
  valGenerator?: ValueGenerator;
  onError?: (
    error: KwaipilotToolSchemaParseError,
    location: KwaipilotToolInputRawTypeLocation,
    schemaStr?: string
  ) => void;
}): {
  headerTypes: KwaipilotToolInputRawType[];
  pathTypes: KwaipilotToolInputRawType[];
  queryTypes: KwaipilotToolInputRawType[];
  bodyTypes: string;
} => {
  const getTypes = (location: KwaipilotToolInputRawTypeLocation) => {
    const schemaStr = schemas?.[location];
    const result = getToolRawTypeFromShallowSchema({
      location,
      schemaStr
    });
    if (result.error) {
      onError?.(result.error, location, schemaStr);
    }
    return result.types;
  };

  const body = getBodyTypesFromSchema({
    schemaStr: schemas?.Body,
    valGenerator: valGenerator ?? ((s) => s.default ?? '')
  });
  if (body.error) {
    onError?.(body.error, KwaipilotToolInputRawTypeLocation.Body);
  }

  return {
    headerTypes: getTypes(KwaipilotToolInputRawTypeLocation.Header),
    pathTypes: getTypes(KwaipilotToolInputRawTypeLocation.Path),
    queryTypes: getTypes(KwaipilotToolInputRawTypeLocation.Query),
    bodyTypes: body.types
  };
};
