import openai from 'openai';
import type {
  ChatCompletion,
  ChatCompletionMessageToolCall as OpenAIChatCompletionMessageToolCall,
  ChatCompletionChunk,
  ChatCompletionMessageParam as OpenAIChatCompletionMessageParam,
  ChatCompletionToolMessageParam as OpenAIChatCompletionToolMessageParam,
  ChatCompletionAssistantMessageParam
} from 'openai/resources';
import { ChatMessageTypeEnum } from './constants';
import { Stream } from 'openai/streaming';

export * from 'openai/resources';

export type ChatCompletionMessageParam = OpenAIChatCompletionMessageParam & {
  dataId?: string;
};
export type ChatCompletionToolMessageParam = OpenAIChatCompletionToolMessageParam & {
  name: string;
};
export type ChatCompletionAssistantToolParam = {
  role: 'assistant';
  tool_calls: ChatCompletionMessageToolCall[];
};

export type ChatCompletionMessageToolCall = OpenAIChatCompletionMessageToolCall & {
  toolName?: string;
  toolAvatar?: string;
};
export type ChatCompletionMessageFunctionCall = ChatCompletionAssistantMessageParam.FunctionCall & {
  id?: string;
  toolName?: string;
  toolAvatar?: string;
};
export type StreamChatType = Stream<ChatCompletionChunk> | ChatCompletion;

export type PromptTemplateItem = {
  title: string;
  desc: string;
  value: string;
};

export default openai;
export * from 'openai';
