import { FlowNodeOutputTypeEnum, FlowNodeTypeEnum } from './node/constant';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  VariableInputEnum,
  variableMap
} from './constants';
import { FlowNodeInputItemType, FlowNodeOutputItemType } from './type/io.d';
import { StoreNodeItemType } from './type';
import type {
  VariableItemType,
  AppTTSConfigType,
  AppWhisperConfigType,
  AppScheduledTriggerConfigType
} from '../app/type';
import { EditorVariablePickerType } from '../../../web/components/common/Textarea/PromptEditor/type';
import { defaultWhisperConfig } from '../app/constants';
import { IS_OVERSEA } from '../../core/chat/constants';

export const getHandleId = (nodeId: string, type: 'source' | 'target', key: string) => {
  return `${nodeId}-${type}-${key}`;
};

export const checkInputIsReference = (input: FlowNodeInputItemType) => {
  const value = input.value;
  if (
    Array.isArray(value) &&
    value.length === 2 &&
    typeof value[0] === 'string' &&
    typeof value[1] === 'string'
  ) {
    return true;
  }
  return false;
};

/* node  */
export const getGuideModule = (modules: StoreNodeItemType[]) =>
  modules.find((item) => item.flowNodeType === FlowNodeTypeEnum.systemConfig);

export const splitGuideModule = (guideModules?: StoreNodeItemType) => {
  const welcomeText: string =
    guideModules?.inputs?.find((item) => item.key === NodeInputKeyEnum.welcomeText)?.value || '';

  const variableModules: VariableItemType[] =
    guideModules?.inputs.find((item) => item.key === NodeInputKeyEnum.variables)?.value || [];

  const questionGuide: boolean =
    !!guideModules?.inputs?.find((item) => item.key === NodeInputKeyEnum.questionGuide)?.value ||
    false;

  const recommendedQuestion: Array<string> = guideModules?.inputs?.find(
    (item) => item.key === NodeInputKeyEnum.recommendedQuestion
  )?.value || ['', '', ''];

  const ttsConfig: AppTTSConfigType = guideModules?.inputs?.find(
    (item) => item.key === NodeInputKeyEnum.tts
  )?.value || { type: 'web' };

  const whisperConfig: AppWhisperConfigType =
    guideModules?.inputs?.find((item) => item.key === NodeInputKeyEnum.whisper)?.value ||
    defaultWhisperConfig;

  const scheduledTriggerConfig: AppScheduledTriggerConfigType | null =
    guideModules?.inputs?.find((item) => item.key === NodeInputKeyEnum.scheduleTrigger)?.value ??
    null;

  return {
    welcomeText,
    variableModules,
    questionGuide,
    ttsConfig,
    whisperConfig,
    scheduledTriggerConfig,
    recommendedQuestion
  };
};

export const getOrInitModuleInputValue = (input: FlowNodeInputItemType) => {
  if (input.value !== undefined || !input.valueType) return input.value;

  const map: Record<string, any> = {
    [WorkflowIOValueTypeEnum.boolean]: false,
    [WorkflowIOValueTypeEnum.number]: 0,
    [WorkflowIOValueTypeEnum.string]: ''
  };

  return map[input.valueType];
};

export const getModuleInputUiField = (input: FlowNodeInputItemType) => {
  // if (input.renderTypeList === FlowNodeInputTypeEnum.input || input.type === FlowNodeInputTypeEnum.textarea) {
  //   return {
  //     placeholder: input.placeholder || input.description
  //   };
  // }
  return {};
};

export const pluginData2FlowNodeIO = (
  nodes: StoreNodeItemType[]
): {
  inputs: FlowNodeInputItemType[];
  outputs: FlowNodeOutputItemType[];
} => {
  const pluginInput = nodes.find((node) => node.flowNodeType === FlowNodeTypeEnum.pluginInput);
  const pluginOutput = nodes.find((node) => node.flowNodeType === FlowNodeTypeEnum.pluginOutput);

  return {
    inputs: pluginInput
      ? pluginInput.inputs.map((item) => ({
          ...item,
          ...getModuleInputUiField(item),
          value: getOrInitModuleInputValue(item),
          canEdit: false
        }))
      : [],
    outputs: pluginOutput
      ? [
          ...pluginOutput.inputs.map((item) => ({
            id: item.key,
            type: FlowNodeOutputTypeEnum.static,
            key: item.key,
            valueType: item.valueType,
            label: item.label || item.key,
            description: item.description
          }))
        ]
      : []
  };
};

export const formatEditorVariablePickerIcon = (
  variables: { key: string; label: string; type?: `${VariableInputEnum}` }[]
): EditorVariablePickerType[] => {
  return variables.map((item) => ({
    ...item,
    icon: item.type ? variableMap[item.type]?.icon : variableMap['input'].icon
  }));
};

// code拼接模板翻译
export const codeJointTemplate = (num: number, it: any) => {
  if (IS_OVERSEA) {
    return `### Code Snippet [${num}]:\n#### Code Info\n- filepath: ${it.path}\n- type: ${it.codeType}\n#### Code Content\n- name: ${it.functionName}\n- signature: ${it.functionSignature}\n- content:\n\`\`\`${it.language}\n${it.code}\n\`\`\``;
  }
  return `### 相关代码片段 [${num}]:\n#### 代码片段基本信息\n- 文件路径: ${it.path}\n- 类型: ${it.codeType}\n#### 代码片段内容\n- 名称: ${it.functionName}\n- 签名: ${it.functionSignature}\n- 内容:\n\`\`\`${it.language}\n${it.code}\n\`\`\``;
};

// File 拼接模板
export const fileJointTemplate = (num: number, it: any) => {
  if (IS_OVERSEA) {
    return `### index[${num}]:\n(Title: ${it.title}, file_idx:${it.fileIndex}, page_num:${it.pageNum}): ${it.content}`;
  }
  return `### 文档片段 [${num}]:\n(文件名:《${it.title}》, 第${it.fileIndex}个文件,第${it.pageNum}页): ${it.content}`;
};

// WEB 拼接模板
export const webJointTemplate = (num: number, it: any) => {
  if (IS_OVERSEA) {
    return `### index[${num}]:\n(Title: ${it.title}): ${it.content}`;
  }
  return `### 文档片段 [${num}]:\n(标题:《${it.title}》,发布日期：${it.date}): ${it.content}`;
};

// Doc 拼接模板
export const docJointTemplate = (num: number, it: any) => {
  if (IS_OVERSEA) {
    return `### index[${num}]:\n(Title: ${it.title}): ${it.text}`;
  }
  return `### 文档片段 [${num}]:\n(文件名:《 ${it.title}》: ${it.text}`;
};
