import { IS_LOCAL, IS_PROD } from '../../core/chat/constants';

export enum FlowNodeTemplateTypeEnum {
  systemInput = 'systemInput',
  tools = 'tools',
  textAnswer = 'textAnswer',
  functionCall = 'functionCall',
  externalCall = 'externalCall',

  personalPlugin = 'personalPlugin',

  other = 'other'
}

export enum WorkflowIOValueTypeEnum {
  string = 'string',
  number = 'number',
  boolean = 'boolean',
  object = 'object',
  arrayString = 'arrayString',
  arrayNumber = 'arrayNumber',
  arrayBoolean = 'arrayBoolean',
  arrayObject = 'arrayObject',
  any = 'any',
  none = 'none',

  chatHistory = 'chatHistory',
  datasetQuote = 'datasetQuote',

  dynamic = 'dynamic',

  // plugin special type
  selectApp = 'selectApp',
  selectDataset = 'selectDataset',
  selectDatasetV2 = 'selectDatasetV2',
  selectTool = 'selectTool',
  selectCodeSearch = 'selectCodeSearch'
}

/* reg: modulename key */
export enum NodeInputKeyEnum {
  // old
  welcomeText = 'welcomeText',
  switch = 'switch', // a trigger switch
  history = 'history',
  userChatInput = 'userChatInput',
  userChatInputFiles = 'userChatInputFiles',
  answerText = 'text',

  // system config
  questionGuide = 'questionGuide',
  // 推荐问题
  recommendedQuestion = 'recommendedQuestion',
  tts = 'tts',
  whisper = 'whisper',
  variables = 'variables',
  scheduleTrigger = 'scheduleTrigger',

  agents = 'agents', // cq agent key

  // latest
  // common
  aiModel = 'model',
  aiSystemPrompt = 'systemPrompt',
  description = 'description',
  anyInput = 'system_anyInput',
  textareaInput = 'system_textareaInput',
  addInputParam = 'system_addInputParam',

  // history
  historyMaxAmount = 'maxContext',

  // ai chat
  aiChatTemperature = 'temperature',
  aiChatMaxToken = 'maxToken',
  aiChatSettingModal = 'aiSettings',
  aiChatIsResponseText = 'isResponseAnswerText',
  aiChatUseInputFiles = 'useInputFiles',
  aiChatAdvancedQuote = 'advancedQuote',
  aiChatQuoteTemplate = 'quoteTemplate',
  aiChatQuotePrompt = 'quotePrompt',
  aiChatDatasetQuote = 'quoteQA',

  // dataset
  datasetSelectList = 'datasets',
  datasetSelectListV2 = 'datasetsV2',
  datasetSimilarity = 'similarity',
  datasetMaxTokens = 'limit',
  datasetSearchMode = 'searchMode',
  datasetSearchUsingReRank = 'usingReRank',
  datasetSearchUsingExtensionQuery = 'datasetSearchUsingExtensionQuery',
  datasetSearchExtensionModel = 'datasetSearchExtensionModel',
  datasetSearchExtensionBg = 'datasetSearchExtensionBg',

  // code search
  codeSearchSelectList = 'codeSearch',

  // tool
  toolSelect = 'toolSelect',

  // context extract
  contextExtractInput = 'content',
  extractKeys = 'extractKeys',

  // http
  httpReqUrl = 'system_httpReqUrl',
  httpHeaders = 'system_httpHeader',
  httpMethod = 'system_httpMethod',
  httpParams = 'system_httpParams',
  httpJsonBody = 'system_httpJsonBody',
  httpPath = 'system_httpPath',
  abandon_httpUrl = 'url',
  httpReqJsonSchema = 'httpReqJsonSchema',

  // kwaipilot tool
  versionId = 'versionId',
  identifier = 'identifier',

  // template-transform
  templateTransformInput = 'template_input',

  // app
  runAppSelectApp = 'app',

  // plugin
  pluginId = 'pluginId',
  pluginStart = 'pluginStart',

  // if else
  condition = 'condition',
  ifElseList = 'ifElseList',

  // kconf
  kconfKey = 'kconfKey',
  kconfEnv = 'kconfEnv',
  // group variable
  groupVariableInput = 'groupVariableInput',

  // multiAgent
  multiAgentSource = 'multiAgentSource',
  multiAgentIdentifier = 'multiAgentIdentifier',
  multiAgentIsResponseText = 'multiAgentIsResponseText',
  multiAgentUseInputFiles = 'multiAgentUseInputFiles'
}

export enum NodeOutputKeyEnum {
  // common
  userChatInput = 'userChatInput',
  userChatInputFiles = 'userChatInputFiles',
  history = 'history',
  answerText = 'answerText', // module answer. the value will be show and save to history
  success = 'success',
  failed = 'failed',
  text = 'system_text',
  addOutputParam = 'system_addOutputParam',

  // dataset
  datasetQuoteQA = 'quoteQA',
  datasetQuoteQAV2 = 'quoteQAV2',

  // code search
  codeSearchQuotaQA = 'codeSearchQuotaQA',

  // web search
  webSearchQuotaQA = 'webSearchQuotaQA',

  // classify
  cqResult = 'cqResult',
  // context extract
  contextExtractFields = 'fields',

  // tf switch
  resultTrue = 'system_resultTrue',
  resultFalse = 'system_resultFalse',

  // tools
  selectedTools = 'selectedTools',

  // http
  httpRawResponse = 'httpRawResponse',
  httpRawResponseSchema = 'httpRawResponseSchema',

  // template transform
  templateTransformOutput = 'template_output',

  // plugin
  pluginStart = 'pluginStart',

  if = 'IF',
  else = 'ELSE',

  // kconf
  kconfResult = 'kconfResult',
  // group variable
  groupVariableOutput = 'groupVariableOutput'
}

export enum VariableInputEnum {
  input = 'input',
  textarea = 'textarea',
  select = 'select',
  external = 'external'
}
export const variableMap = {
  [VariableInputEnum.input]: {
    icon: 'core/app/variable/input',
    title: 'core.module.variable.input type',
    desc: ''
  },
  [VariableInputEnum.textarea]: {
    icon: 'core/app/variable/textarea',
    title: 'core.module.variable.textarea type',
    desc: '允许用户最多输入4000字的对话框。'
  },
  [VariableInputEnum.select]: {
    icon: 'core/app/variable/select',
    title: 'core.module.variable.select type',
    desc: ''
  },
  [VariableInputEnum.external]: {
    icon: 'core/app/variable/external',
    title: 'core.module.variable.External type',
    desc: '可以通过API接口或分享链接的Query传递变量。增加该类型变量的主要目的是用于变量提示。使用例子: 你可以通过分享链接Query中拼接Token，来实现内部系统身份鉴权。'
  }
};

export const DYNAMIC_INPUT_REFERENCE_KEY = 'DYNAMIC_INPUT_REFERENCE_KEY';

/* run time */
export enum RuntimeEdgeStatusEnum {
  'waiting' = 'waiting',
  'active' = 'active',
  'skipped' = 'skipped'
}

export const VARIABLE_NODE_ID = 'VARIABLE_NODE_ID';

export const ModuleIcons = {
  Start:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/start.73ff1b8785bb8732.svg',
  GlobalVariable:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/global-vars.f613b7d940030940.svg',
  IfElse:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/if-else.3ff28a0e3daf26a8.svg',
  AIChat:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/ai.952d8652ea03fe4a.svg',
  Reply:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/reply.1770e90430f6ea53.svg',
  ClassifyQuestion:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/cq.955d9dc1cff24351.svg',
  Extract:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/extract.2d62e476cb724c6f.svg',
  Dataset:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/db.86881d2cbe57da23.svg',
  HTTP: 'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/http.945b2b32652e4293.svg',
  Kconf:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/kconf.712e4e2db1ea6f1e.svg',
  Tool: 'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/tool.d0012432c9afaded.svg',
  SystemConfig:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/user-guide.31f7cf5b64fe0649.svg',
  CodeInterpreter:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/code.5b86477e26041756.svg',
  Aggregation:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/agg.b5c4cfa13dbed7c8.svg',
  Template:
    'https://h1.static.yximgs.com/kcdn/cdn-kcdn112115/manual-upload/workflow-icons/tpl.b39cde38777eccf3.svg'
} as const;

let WEB_GEN_URL = 'http://kwaipilot-tool.internal/node/api/webpage/generate';
if (IS_LOCAL) {
  WEB_GEN_URL = 'http://localhost:8080/api/webpage/generate';
} else if (!IS_PROD) {
  WEB_GEN_URL = 'https://kwaipilot-tool.corp.kuaishou.com/node/api/webpage/generate';
}

let CODE_INTERPRETER_URL = 'http://code-interpreter.internal/-/api/agent/chat/completions';
if (IS_LOCAL) {
  CODE_INTERPRETER_URL = 'http://localhost:3000/-/api/agent/chat/completions';
} else if (!IS_PROD) {
  CODE_INTERPRETER_URL = 'https://code-interpreter.corp.kuaishou.com/-/api/agent/chat/completions';
}
export const INTERNAL_AGENT_MAP: Record<string, string> = {
  WebGen: WEB_GEN_URL,
  CodeInterpreter: CODE_INTERPRETER_URL
};
