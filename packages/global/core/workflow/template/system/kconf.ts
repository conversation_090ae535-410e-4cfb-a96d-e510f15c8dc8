import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../node/constant';
import { FlowNodeTemplateType } from '../../type/index.d';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  FlowNodeTemplateTypeEnum,
  ModuleIcons
} from '../../constants';
import { getHandleConfig } from '../utils';

export const KconfModule: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.kconf,
  templateType: FlowNodeTemplateTypeEnum.externalCall,
  flowNodeType: FlowNodeTypeEnum.kconf,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  name: 'Kconf 请求',
  intro: '可以获取kconf数据，要求kconf数据能够公开访问',
  showStatus: true,
  avatar: ModuleIcons.Kconf,
  inputs: [
    {
      key: NodeInputKeyEnum.kconfKey,
      renderTypeList: [FlowNodeInputTypeEnum.input, FlowNodeInputTypeEnum.reference],
      valueType: WorkflowIOValueTypeEnum.string,
      label: 'Kconf key',
      description: 'kconf的key',
      value: '',
      required: true
    }
    // {
    //   key: NodeInputKeyEnum.kconfEnv,
    //   renderTypeList: [FlowNodeInputTypeEnum.select],
    //   list: [
    //     {
    //       label: 'staging',
    //       value: 'staging'
    //     },
    //     {
    //       label: 'test',
    //       value: 'test'
    //     },
    //     {
    //       label: 'prod',
    //       value: 'prod'
    //     }
    //   ],
    //   valueType: WorkflowIOValueTypeEnum.string,
    //   label: 'Kconf 环境',
    //   description: 'kconf的环境',
    //   value: 'prod',
    //   required: true
    // }
  ],
  outputs: [
    {
      id: NodeOutputKeyEnum.kconfResult,
      key: NodeOutputKeyEnum.kconfResult,
      label: '结果',
      description: 'kconf返回的数据',
      valueType: WorkflowIOValueTypeEnum.any,
      type: FlowNodeOutputTypeEnum.static
    }
  ]
};
