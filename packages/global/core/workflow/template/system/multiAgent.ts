import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../node/constant';
import { FlowNodeTemplateType } from '../../type';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  FlowNodeTemplateTypeEnum
} from '../../constants';
import { Input_Template_History, Input_Template_UserChatInput } from '../input';
import { getHandleConfig } from '../utils';
import { ModuleIcons } from '../../constants';

export const MultiAgentModule: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.multiAgent,
  templateType: FlowNodeTemplateTypeEnum.textAnswer,
  flowNodeType: FlowNodeTypeEnum.multiAgent,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: ModuleIcons.AIChat,
  name: '智能体调用',
  intro: '调用其他智能体，包括 Kwaipilot 智能体或其他外部智能体',
  showStatus: true,
  isTool: true,
  inputs: [
    {
      key: NodeInputKeyEnum.multiAgentSource,
      renderTypeList: [FlowNodeInputTypeEnum.input, FlowNodeInputTypeEnum.reference],
      label: '智能体来源',
      value: '',
      valueType: WorkflowIOValueTypeEnum.string
    },
    {
      key: NodeInputKeyEnum.multiAgentIdentifier,
      renderTypeList: [FlowNodeInputTypeEnum.input, FlowNodeInputTypeEnum.reference],
      label: '智能体标识',
      value: '',
      valueType: WorkflowIOValueTypeEnum.string
    },
    {
      key: NodeInputKeyEnum.multiAgentIsResponseText,
      renderTypeList: [FlowNodeInputTypeEnum.switch],
      label: '是否展示回答',
      value: '',
      valueType: WorkflowIOValueTypeEnum.boolean
    },
    {
      key: NodeInputKeyEnum.multiAgentUseInputFiles,
      renderTypeList: [FlowNodeInputTypeEnum.switch],
      label: '是否使用用户文件',
      value: '',
      valueType: WorkflowIOValueTypeEnum.boolean
    },
    Input_Template_History,
    { ...Input_Template_UserChatInput, toolDescription: '用户问题' }
  ],
  outputs: [
    {
      id: NodeOutputKeyEnum.history,
      key: NodeOutputKeyEnum.history,
      label: 'core.module.output.label.New context',
      description: 'core.module.output.description.New context',
      valueType: WorkflowIOValueTypeEnum.chatHistory,
      type: FlowNodeOutputTypeEnum.static
    },
    {
      id: NodeOutputKeyEnum.answerText,
      key: NodeOutputKeyEnum.answerText,
      label: 'core.module.output.label.Ai response content',
      description: 'core.module.output.description.Ai response content',
      valueType: WorkflowIOValueTypeEnum.string,
      type: FlowNodeOutputTypeEnum.static
    }
  ]
};
