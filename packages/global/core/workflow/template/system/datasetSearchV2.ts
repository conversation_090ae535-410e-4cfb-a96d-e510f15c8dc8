import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../node/constant';
import { FlowNodeTemplateType } from '../../type';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  FlowNodeTemplateTypeEnum
} from '../../constants';
import { Input_Template_UserChatInput } from '../input';
import { DatasetSearchModeEnum } from '../../../dataset/constants';
import { getHandleConfig } from '../utils';
import { ModuleIcons } from '../../constants';

export const Dataset_SEARCH_DESC =
  '调用“语义检索”和“全文检索”能力，从“知识库”中查找可能与问题相关的参考内容';

export const DatasetSearchModuleV2: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.datasetSearchNodeV2,
  templateType: FlowNodeTemplateTypeEnum.functionCall,
  flowNodeType: FlowNodeTypeEnum.datasetSearchNodeV2,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: ModuleIcons.Dataset,
  name: '知识库搜索',
  intro: Dataset_SEARCH_DESC,
  showStatus: true,
  isTool: true,
  inputs: [
    {
      key: NodeInputKeyEnum.datasetSelectListV2,
      renderTypeList: [FlowNodeInputTypeEnum.selectDatasetV2],
      // renderTypeList: [FlowNodeInputTypeEnum.selectDataset, FlowNodeInputTypeEnum.reference],
      label: 'core.module.input.label.Select dataset',
      value: [],
      valueType: WorkflowIOValueTypeEnum.selectDatasetV2,
      list: [],
      required: true
    },
    // {
    //   key: NodeInputKeyEnum.datasetSimilarity,
    //   renderTypeList: [FlowNodeInputTypeEnum.selectDatasetParamsModal],
    //   label: '',
    //   value: 0.4,
    //   valueType: WorkflowIOValueTypeEnum.number
    // },
    // setting from modal
    // {
    //   key: NodeInputKeyEnum.datasetMaxTokens,
    //   renderTypeList: [FlowNodeInputTypeEnum.hidden],
    //   label: '',
    //   value: 1500,
    //   valueType: WorkflowIOValueTypeEnum.number
    // },
    // {
    //   key: NodeInputKeyEnum.datasetSearchMode,
    //   renderTypeList: [FlowNodeInputTypeEnum.hidden],
    //   label: '',
    //   valueType: WorkflowIOValueTypeEnum.string,
    //   value: DatasetSearchModeEnum.embedding
    // },
    // {
    //   key: NodeInputKeyEnum.datasetSearchUsingReRank,
    //   renderTypeList: [FlowNodeInputTypeEnum.hidden],
    //   label: '',
    //   valueType: WorkflowIOValueTypeEnum.boolean,
    //   value: false
    // },
    // {
    //   key: NodeInputKeyEnum.datasetSearchUsingExtensionQuery,
    //   renderTypeList: [FlowNodeInputTypeEnum.hidden],
    //   label: '',
    //   valueType: WorkflowIOValueTypeEnum.boolean,
    //   value: true
    // },
    // {
    //   key: NodeInputKeyEnum.datasetSearchExtensionModel,
    //   renderTypeList: [FlowNodeInputTypeEnum.hidden],
    //   label: '',
    //   valueType: WorkflowIOValueTypeEnum.string
    // },
    // {
    //   key: NodeInputKeyEnum.datasetSearchExtensionBg,
    //   renderTypeList: [FlowNodeInputTypeEnum.hidden],
    //   label: '',
    //   valueType: WorkflowIOValueTypeEnum.string,
    //   value: ''
    // },
    {
      ...Input_Template_UserChatInput,
      toolDescription: '需要检索的内容'
    }
  ],
  outputs: [
    // {
    //   id: NodeOutputKeyEnum.datasetQuoteQA,
    //   key: NodeOutputKeyEnum.datasetQuoteQA,
    //   label: 'core.module.Dataset quote.label',
    //   description: '特殊数组格式，搜索结果为空时，返回空数组。',
    //   type: FlowNodeOutputTypeEnum.static,
    //   valueType: WorkflowIOValueTypeEnum.datasetQuote
    // }
    {
      id: NodeOutputKeyEnum.datasetQuoteQAV2,
      key: NodeOutputKeyEnum.datasetQuoteQAV2,
      label: 'core.module.Dataset quote.label',
      // description: '特殊数组格式，搜索结果为空时，返回空数组。',
      type: FlowNodeOutputTypeEnum.static,
      valueType: WorkflowIOValueTypeEnum.arrayString
    }
  ]
};
