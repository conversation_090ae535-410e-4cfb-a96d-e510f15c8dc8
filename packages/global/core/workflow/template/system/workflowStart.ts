import { FlowNodeOutputTypeEnum, FlowNodeTypeEnum } from '../../node/constant';
import { FlowNodeTemplateType } from '../../type/index.d';
import {
  WorkflowIOValueTypeEnum,
  NodeOutputKeyEnum,
  FlowNodeTemplateTypeEnum,
  ModuleIcons
} from '../../constants';
import { getHandleConfig } from '../utils';
import { Input_Template_UserChatInput, Input_Template_UserChatInputFiles } from '../input';

export const getUserFilesInputItem = () => {
  return {
    ...Input_Template_UserChatInputFiles,
    toolDescription: '用户文件'
  };
}

export const getUserFilesOutputItem = () => {
  return {
    id: NodeOutputKeyEnum.userChatInputFiles,
    key: NodeOutputKeyEnum.userChatInputFiles,
    label: 'core.module.input.label.user files',
    type: FlowNodeOutputTypeEnum.static,
    valueType: WorkflowIOValueTypeEnum.arrayObject
  };
}

export const WorkflowStart: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.workflowStart,
  templateType: FlowNodeTemplateTypeEnum.systemInput,
  flowNodeType: FlowNodeTypeEnum.workflowStart,
  sourceHandle: getHandleConfig(false, true, false, false),
  targetHandle: getHandleConfig(false, false, false, false),
  avatar: ModuleIcons.Start,
  name: '流程开始',
  intro: '',
  forbidDelete: true,
  unique: true,
  inputs: [
    {
      ...Input_Template_UserChatInput,
      toolDescription: '用户问题'
    },
    getUserFilesInputItem()
  ],
  outputs: [
    {
      id: NodeOutputKeyEnum.userChatInput,
      key: NodeOutputKeyEnum.userChatInput,
      label: 'core.module.input.label.user question',
      type: FlowNodeOutputTypeEnum.static,
      valueType: WorkflowIOValueTypeEnum.string
    },
    getUserFilesOutputItem()
  ]
};
