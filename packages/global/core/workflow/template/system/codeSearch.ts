import {
  FlowNodeInputTypeEnum,
  FlowNodeOutputTypeEnum,
  FlowNodeTypeEnum
} from '../../node/constant';
import { FlowNodeTemplateType } from '../../type';
import {
  WorkflowIOValueTypeEnum,
  NodeInputKeyEnum,
  NodeOutputKeyEnum,
  FlowNodeTemplateTypeEnum
} from '../../constants';
import { Input_Template_UserChatInput } from '../input';
import { getHandleConfig } from '../utils';
import { ModuleIcons } from '../../constants';

export const CodeSearchModule: FlowNodeTemplateType = {
  id: FlowNodeTypeEnum.codeSearch,
  templateType: FlowNodeTemplateTypeEnum.functionCall,
  flowNodeType: FlowNodeTypeEnum.codeSearch,
  sourceHandle: getHandleConfig(true, true, true, true),
  targetHandle: getHandleConfig(true, true, true, true),
  avatar: ModuleIcons.Dataset,
  name: '代码搜索',
  intro: '使用 RAG2.0 for Code 能力，从代码库中查找可能与问题相关的参考代码',
  showStatus: true,
  isTool: true,
  inputs: [
    {
      key: NodeInputKeyEnum.codeSearchSelectList,
      renderTypeList: [FlowNodeInputTypeEnum.selectCodeSearch],
      label: 'core.module.input.label.Select repo',
      value: [],
      valueType: WorkflowIOValueTypeEnum.selectCodeSearch,
      list: [],
      required: true
    },
    {
      ...Input_Template_UserChatInput,
      toolDescription: '需要检索的内容'
    }
  ],
  outputs: [
    {
      id: NodeOutputKeyEnum.codeSearchQuotaQA,
      key: NodeOutputKeyEnum.codeSearchQuotaQA,
      label: 'core.module.CodeSearch quote.label',
      type: FlowNodeOutputTypeEnum.static,
      valueType: WorkflowIOValueTypeEnum.arrayString
    }
  ]
};
