import { ClassifyQuestionAgentItemType } from '../workflow/type';
import { SearchDataResponseItemType } from '../dataset/type';
import {
  ChatFileTypeEnum,
  ChatItemValueTypeEnum,
  ChatRoleEnum,
  ChatSourceEnum,
  ChatStatusEnum
} from './constants';
import { FlowNodeTypeEnum } from '../workflow/node/constant';
import { NodeOutputKeyEnum } from '../workflow/constants';
import { DispatchNodeResponseKeyEnum } from '../workflow/runtime/constants';
import { AppSchema } from '../app/type';
import type { AppSchema as AppType } from '../app/type';
import { DatasetSearchModeEnum } from '../dataset/constants';
import { ChatBoxInputType } from '../../../../projects/app/src/components/ChatBox/type';
import { DispatchNodeResponseType } from '../workflow/runtime/type.d';

export type ChatSchema = {
  _id: string;
  chatId: string;
  userId: string;
  teamId: string;
  tmbId: string;
  appId: string;
  updateTime: Date;
  title: string;
  customTitle: string;
  top: boolean;
  variables: Record<string, any>;
  source: `${ChatSourceEnum}`;
  shareId?: string;
  outLinkUid?: string;
  content: ChatItemType[];
  metadata?: Record<string, any>;
};

export type ChatWithAppSchema = Omit<ChatSchema, 'appId'> & {
  appId: AppSchema;
};

export type RequestUserChatFile = {
  type: `${ChatFileTypeEnum}`;
  name: string;
  url: string;
};

export type UserChatFile = {
  type: `${ChatFileTypeEnum}`;
  name: string;
  url: string;
  content: string;
  index: number;
  pages: Array<{
    pageNumber: number;
    content: string;
  }>;
};

export type UserChatFileWithoutContent = Omit<UserChatFile, 'content' | 'pages' | 'index'>;

export type UserChatItemValueItemType = {
  type: ChatItemValueTypeEnum.text | ChatItemValueTypeEnum.file;
  text?: {
    content: string;
  };
  file?: UserChatFile;
};
export type UserChatItemType = {
  obj: ChatRoleEnum.Human;
  value: UserChatItemValueItemType[];
};
export type SystemChatItemValueItemType = {
  type: ChatItemValueTypeEnum.text;
  text?: {
    content: string;
  };
};
export type SystemChatItemType = {
  obj: ChatRoleEnum.System;
  value: SystemChatItemValueItemType[];
};
export type AIChatItemValueItemType = {
  type: ChatItemValueTypeEnum.text | ChatItemValueTypeEnum.tool;
  text?: {
    content: string;
  };
  tools?: ToolModuleResponseItemType[];
};
export type AIChatItemType = {
  obj: ChatRoleEnum.AI;
  value: AIChatItemValueItemType[];
  userGoodFeedback?: string;
  userBadFeedback?: string;
  customFeedbacks?: string[];
  adminFeedback?: AdminFbkType;
  [DispatchNodeResponseKeyEnum.nodeResponse]?: ChatHistoryItemResType[];
  [DispatchNodeResponseKeyEnum.artifacts]?: ArtifactChatItem[];
};
export type ChatItemValueItemType =
  | UserChatItemValueItemType
  | SystemChatItemValueItemType
  | AIChatItemValueItemType;

export type ChatItemSchema = (UserChatItemType | SystemChatItemType | AIChatItemType) & {
  dataId: string;
  chatId: string;
  userId: string;
  teamId: string;
  tmbId: string;
  appId: string;
  time: Date;
};

export type AdminFbkType = {
  dataId: string;
  datasetId: string;
  collectionId: string;
  q: string;
  a?: string;
};

/* --------- chat item ---------- */
export type ChatItemType = (UserChatItemType | SystemChatItemType | AIChatItemType) & {
  dataId?: string;
};

export type ChatSiteItemType = (UserChatItemType | SystemChatItemType | AIChatItemType) & {
  dataId: string;
  status: `${ChatStatusEnum}`;
  moduleName?: string;
  ttsBuffer?: Uint8Array;
} & ChatBoxInputType;

/* ---------- history ------------- */
export type HistoryItemType = {
  chatId: string;
  updateTime: Date;
  customTitle?: string;
  title: string;
};
export type ChatHistoryItemType = HistoryItemType & {
  appId: string;
  top: boolean;
};

/* ------- response data ------------ */
export type ChatHistoryItemResType = DispatchNodeResponseType & {
  nodeId: string;
  moduleType: `${FlowNodeTypeEnum}`;
  moduleName: string;
};

/* One tool run response  */
export type ToolRunResponseItemType = any;
/* tool module response */
export type ToolModuleResponseItemType = {
  id: string;
  toolName: string; // tool name
  toolAvatar: string;
  params: string; // tool params
  response: string;
  functionName: string;
};

/* dispatch run time */
export type RuntimeUserPromptType = {
  files?: UserChatItemValueItemType['file'][];
  text: string;
};

/********************************************************/
/******************* artifact 数据类型 *******************/
/*********************** 过程数据 ************************/
// 代码生成
export type ArtifactProcessChatItemCodeGen = {
  category: 'code-gen';
  content: {
    file: string;
    delta: {
      code: string;
    };
  };
};

export type ArtifactProcessChatItem = {
  type: 'process';
  data: ArtifactProcessChatItemCodeGen;
};

/*********************** 结果数据 ************************/
// 网页预览
export type ArtifactResultChatItemWebPreview = {
  category: 'web';
  content: {
    url: string;
    originCode: string;
  };
};

export type ArtifactResultChatItemWebIDEPreview = {
  category: 'webide';
  content: {
    url: string;
  };
};

// 文件预览
export type ArtifactResultChatItemFilePreview = {
  category: 'file';
  content: {
    files: Array<{
      name: string;
      url: string;
      type: 'image' | 'pdf' | 'csv' | 'file';
    }>;
  };
};

export type ArtifactResultChatItem = {
  type: 'result';
  data:
    | ArtifactResultChatItemWebPreview
    | ArtifactResultChatItemWebIDEPreview
    | ArtifactResultChatItemFilePreview;
};
/********************************************************/
export type ArtifactChatItem = ArtifactResultChatItem | ArtifactProcessChatItem;
/********************************************************/
/********************************************************/
