import type {
  ChatItemType,
  ChatItemValueItemType,
  RuntimeUserPromptType,
  UserChatItemType
} from '../../core/chat/type.d';
import {
  ChatFileTypeEnum,
  ChatItemValueTypeEnum,
  ChatRoleEnum,
  DEFAULT_MODEL_CONFIG,
  FILE_CONTEXT_LENGTH_RATIO
} from './constants';
import type {
  ChatCompletionContentPart,
  ChatCompletionContentPartText,
  ChatCompletionFunctionMessageParam,
  ChatCompletionMessageFunctionCall,
  ChatCompletionMessageParam,
  ChatCompletionMessageToolCall,
  ChatCompletionToolMessageParam
} from '../../core/ai/type.d';
import { ChatCompletionRequestMessageRoleEnum } from '../../core/ai/constants';
import { createLogger } from '../../common/util/logger';
import { replaceVariable } from '../../common/string/tools';

const GPT2Chat = {
  [ChatCompletionRequestMessageRoleEnum.System]: ChatRoleEnum.System,
  [ChatCompletionRequestMessageRoleEnum.User]: ChatRoleEnum.Human,
  [ChatCompletionRequestMessageRoleEnum.Assistant]: ChatRoleEnum.AI,
  [ChatCompletionRequestMessageRoleEnum.Function]: ChatRoleEnum.AI,
  [ChatCompletionRequestMessageRoleEnum.Tool]: ChatRoleEnum.AI
};

const logger = createLogger('chat-adapt');
const EMPTY_FILE_CONTENT = '<EMPTY_FILE>';

export function adaptRole_Message2Chat(role: `${ChatCompletionRequestMessageRoleEnum}`) {
  return GPT2Chat[role];
}

export const simpleUserContentPart = (content: ChatCompletionContentPart[]) => {
  if (content.length === 1 && content[0].type === 'text') {
    return content[0].text;
  }

  const list: ChatCompletionContentPart[] = [];
  const isTextItem = (item: ChatCompletionContentPart): item is ChatCompletionContentPartText =>
    item.type === 'text';
  content.forEach((item) => {
    if (item.type !== 'text') {
      list.push(item);
      return;
    }

    const textItem = list.find((i) => isTextItem(i));
    // 如果没有textItem，或者textItem的text为空，直接push
    if (!textItem) {
      list.push({
        type: 'text',
        text: item.text
      });
      return;
    }
    (textItem as ChatCompletionContentPartText).text += `\n${item.text}`;
  });
  return list;
};

export const chats2GPTMessages = ({
  messages,
  reserveId,
  reserveTool = false,
  supportImg = false,
  maxContextLength = DEFAULT_MODEL_CONFIG.maxContext
}: {
  messages: ChatItemType[];
  reserveId: boolean;
  reserveTool?: boolean;
  supportImg?: boolean;
  maxContextLength?: number;
}): ChatCompletionMessageParam[] => {
  let results: ChatCompletionMessageParam[] = [];
  let totalFileLength = 0;
  const maxContextFileLength = maxContextLength * FILE_CONTEXT_LENGTH_RATIO;
  const systemMessages = messages.filter((item) => item.obj === ChatRoleEnum.System);
  const originalNonSystemMessages = messages.filter((item) => item.obj !== ChatRoleEnum.System);

  // 从最新的消息开始处理，截取文件内容
  const nonSystemMessages = [...originalNonSystemMessages]
    .reverse()
    .map((item) => {
      if (item.obj !== ChatRoleEnum.Human) {
        return { ...item };
      }

      const value = item.value.map((v) => {
        if (v.type === ChatItemValueTypeEnum.file && v.file?.type === ChatFileTypeEnum.file) {
          const file = v.file;
          // 限制文件大小
          if (totalFileLength >= maxContextFileLength) {
            logger.info(
              `file content length ${file.content.length} is greater than ${maxContextFileLength}, ${file.name} ${file.url}`
            );
            return {
              ...v,
              file: {
                ...file,
                content: EMPTY_FILE_CONTENT
              }
            };
          }
          const lenLeft = maxContextFileLength - totalFileLength;
          const content =
            lenLeft < file.content.length ? file.content.slice(0, lenLeft) : file.content;
          totalFileLength += file.content.length;
          return {
            ...v,
            file: {
              ...file,
              content
            }
          };
        }
        return { ...v };
      });

      return {
        ...item,
        value
      };
    })
    .reverse();

  // 按原先的顺序，将消息转换为GPT消息
  nonSystemMessages.forEach((item) => {
    if (results.length === 0 && item.obj !== ChatRoleEnum.Human) {
      logger.warn(
        'The input messages (excluding the system message) must start with a user message'
      );
    }

    const dataId = reserveId ? item.dataId : undefined;
    if (item.obj === ChatRoleEnum.Human) {
      const value = item.value
        .map((item) => {
          if (item.type === ChatItemValueTypeEnum.text) {
            return {
              type: 'text',
              text: item.text?.content || ''
            };
          }
          const file = item.file;
          if (file && item.type === ChatItemValueTypeEnum.file) {
            // 图片
            if (supportImg && file.type === ChatFileTypeEnum.image) {
              return {
                type: 'image_url',
                image_url: {
                  url: file.url || ''
                }
              };
            }
            // 文本文件
            if (file.type === ChatFileTypeEnum.file) {
              const content = file.content;
              const contentText =
                content === EMPTY_FILE_CONTENT
                  ? 'content is too long, so it is not displayed\n'
                  : `content:\n${content}`;
              return {
                type: 'text',
                text: `filename:\n${file.name}\n\n${contentText}`
              };
            }
          }
          return null;
        })
        .filter(Boolean) as ChatCompletionContentPart[];

      results.push({
        dataId,
        role: ChatCompletionRequestMessageRoleEnum.User,
        content: simpleUserContentPart(value)
      });
    } else {
      //AI
      item.value.forEach((value) => {
        if (value.type === ChatItemValueTypeEnum.tool && value.tools && reserveTool) {
          const tool_calls: ChatCompletionMessageToolCall[] = [];
          const toolResponse: ChatCompletionToolMessageParam[] = [];
          value.tools.forEach((tool) => {
            tool_calls.push({
              id: tool.id,
              type: 'function',
              function: {
                name: tool.functionName,
                arguments: tool.params
              }
            });
            toolResponse.push({
              tool_call_id: tool.id,
              role: ChatCompletionRequestMessageRoleEnum.Tool,
              name: tool.functionName,
              content: tool.response
            });
          });
          results = results
            .concat({
              dataId,
              role: ChatCompletionRequestMessageRoleEnum.Assistant,
              tool_calls
            })
            .concat(toolResponse);
        } else if (value.text) {
          results.push({
            dataId,
            role: ChatCompletionRequestMessageRoleEnum.Assistant,
            content: value.text.content
          });
        }
      });
    }
  });

  // 如果系统提示不为空，则将系统提示添加到结果的开头
  const systemContent = systemMessages.map((item) => item.value?.[0]?.text?.content).join('\n');
  if (systemContent) {
    results.unshift({
      dataId: reserveId ? systemMessages[0].dataId : undefined,
      role: ChatCompletionRequestMessageRoleEnum.System,
      content: systemContent
    });
  }

  return results;
};
export const GPTMessages2Chats = (
  messages: ChatCompletionMessageParam[],
  reserveTool = true
): ChatItemType[] => {
  return messages
    .map((item) => {
      const value: ChatItemType['value'] = [];
      const obj = GPT2Chat[item.role];

      if (
        obj === ChatRoleEnum.System &&
        item.role === ChatCompletionRequestMessageRoleEnum.System
      ) {
        value.push({
          type: ChatItemValueTypeEnum.text,
          text: {
            content: item.content
          }
        });
      } else if (
        obj === ChatRoleEnum.Human &&
        item.role === ChatCompletionRequestMessageRoleEnum.User
      ) {
        if (typeof item.content === 'string') {
          value.push({
            type: ChatItemValueTypeEnum.text,
            text: {
              content: item.content
            }
          });
        } else if (Array.isArray(item.content)) {
          item.content.forEach((item) => {
            if (item.type === 'text') {
              value.push({
                type: ChatItemValueTypeEnum.text,
                text: {
                  content: item.text
                }
              });
            } else if (item.type === 'image_url') {
              value.push({
                //@ts-ignore
                type: 'file',
                file: {
                  type: ChatFileTypeEnum.image,
                  name: '',
                  url: item.image_url.url,
                  content: '',
                  index: -1,
                  pages: []
                }
              });
            }
          });
          // @ts-ignore
        }
      } else if (
        obj === ChatRoleEnum.AI &&
        item.role === ChatCompletionRequestMessageRoleEnum.Assistant
      ) {
        if (item.content && typeof item.content === 'string') {
          value.push({
            type: ChatItemValueTypeEnum.text,
            text: {
              content: item.content
            }
          });
        } else if (item.tool_calls && reserveTool) {
          // save tool calls
          const toolCalls = item.tool_calls as ChatCompletionMessageToolCall[];
          value.push({
            //@ts-ignore
            type: ChatItemValueTypeEnum.tool,
            tools: toolCalls.map((tool) => {
              let toolResponse =
                messages.find(
                  (msg) =>
                    msg.role === ChatCompletionRequestMessageRoleEnum.Tool &&
                    msg.tool_call_id === tool.id
                )?.content || '';
              toolResponse =
                typeof toolResponse === 'string' ? toolResponse : JSON.stringify(toolResponse);

              return {
                id: tool.id,
                toolName: tool.toolName || '',
                toolAvatar: tool.toolAvatar || '',
                functionName: tool.function.name,
                params: tool.function.arguments,
                response: toolResponse as string
              };
            })
          });
        } else if (item.function_call && reserveTool) {
          const functionCall = item.function_call as ChatCompletionMessageFunctionCall;
          const functionResponse = messages.find(
            (msg) =>
              msg.role === ChatCompletionRequestMessageRoleEnum.Function &&
              msg.name === item.function_call?.name
          ) as ChatCompletionFunctionMessageParam;

          if (functionResponse) {
            value.push({
              //@ts-ignore
              type: ChatItemValueTypeEnum.tool,
              tools: [
                {
                  id: functionCall.id || '',
                  toolName: functionCall.toolName || '',
                  toolAvatar: functionCall.toolAvatar || '',
                  functionName: functionCall.name,
                  params: functionCall.arguments,
                  response: functionResponse.content || ''
                }
              ]
            });
          }
        }
      }

      return {
        dataId: item.dataId,
        obj,
        value
      } as ChatItemType;
    })
    .filter((item) => item.value.length > 0);
};

export const chatValue2RuntimePrompt = (value: ChatItemValueItemType[]): RuntimeUserPromptType => {
  const prompt: RuntimeUserPromptType = {
    files: [],
    text: ''
  };
  value.forEach((item) => {
    if (item.type === 'file' && item.file) {
      prompt.files?.push(item.file);
    } else if (item.text) {
      prompt.text += item.text.content;
    }
  });
  return prompt;
};

export const runtimePrompt2ChatsValue = (
  prompt: RuntimeUserPromptType
): UserChatItemType['value'] => {
  const value: UserChatItemType['value'] = [];
  if (prompt.files) {
    prompt.files.forEach((file) => {
      value.push({
        type: ChatItemValueTypeEnum.file,
        file
      });
    });
  }
  if (prompt.text) {
    value.push({
      type: ChatItemValueTypeEnum.text,
      text: {
        content: prompt.text
      }
    });
  }
  return value;
};

export const getSystemPrompt = (prompt?: string): ChatItemType[] => {
  if (!prompt) return [];

  const systemPrompt = replaceVariable(prompt, {
    date: new Date().toLocaleString()
  });
  return [
    {
      obj: ChatRoleEnum.System,
      value: [{ type: ChatItemValueTypeEnum.text, text: { content: systemPrompt } }]
    }
  ];
};
