import type { ModelConfig } from '../../core/ai/model.d';

export const IS_PROD = process.env.APP_ENV === 'production';
export const IS_PREONLINE = process.env.KWS_SERVICE_STAGE === 'PREONLINE';
export const IS_LOCAL = !process.env.MY_POD_NAME;
export const IS_OVERSEA = !!process.env.IS_OVERSEA;
export const LLM_CALLING_KAFKA_TOPIC = 'ai_devops_gateway_calling_log';
export const CENSOR_DATA_KAFKA_TOPIC = 'kwaipilot_censor_data';
// 海外版默认为西七区
export const OVERSEA_TIMEZONE = -7;

export enum ChatRoleEnum {
  System = 'System',
  Human = 'Human',
  AI = 'AI'
}
export const ChatRoleMap = {
  [ChatRoleEnum.System]: {
    name: '系统'
  },
  [ChatRoleEnum.Human]: {
    name: '用户'
  },
  [ChatRoleEnum.AI]: {
    name: 'AI'
  }
};

export enum ChatFileTypeEnum {
  image = 'image',
  file = 'file'
}
export enum ChatItemValueTypeEnum {
  text = 'text',
  file = 'file',
  tool = 'tool'
}

export enum ChatSourceEnum {
  test = 'test',
  online = 'online',
  share = 'share',
  api = 'api',
  team = 'team'
}
export const ChatSourceMap = {
  [ChatSourceEnum.test]: {
    name: 'core.chat.logs.test'
  },
  [ChatSourceEnum.online]: {
    name: 'core.chat.logs.online'
  },
  [ChatSourceEnum.share]: {
    name: 'core.chat.logs.share'
  },
  [ChatSourceEnum.api]: {
    name: 'core.chat.logs.api'
  },
  [ChatSourceEnum.team]: {
    name: 'core.chat.logs.team'
  }
};

export enum ChatStatusEnum {
  loading = 'loading',
  running = 'running',
  finish = 'finish'
}

export const IMG_BLOCK_KEY = 'img-block';
export const FILE_BLOCK_KEY = 'file-block';

export const MARKDOWN_QUOTE_SIGN = 'QUOTE SIGN';

export const DEFAULT_MODEL_CONFIG: ModelConfig = {
  maxContext: 4096,
  maxResponse: 4096,
  supportImg: false
};

// 32k 模型输入+输出长度，60% 作为实际输入长度（@linzheng)
const KWAIPILOT_32K_CONTEXT_MAX = (IS_PROD ? 32000 : 15000) * 0.6;
const KWAIPILOT_128K_CONTEXT_MAX = 64000 * 0.6;

export const FILE_CONTEXT_LENGTH_RATIO = 1.5;

export const ModelConfigStore: Record<string, ModelConfig> = {
  'gpt-3.5-turbo': {
    maxContext: 16385,
    maxResponse: 4096,
    supportImg: false
  },
  'gpt-4o': {
    maxContext: 128 * 1000,
    maxResponse: 4096,
    supportImg: true
  },
  kwaipilot_pro_32k: {
    maxContext: KWAIPILOT_32K_CONTEXT_MAX,
    maxResponse: 4096,
    supportImg: false
  },
  kwaipilot_turbo_128k: {
    maxContext: KWAIPILOT_128K_CONTEXT_MAX,
    maxResponse: 4096,
    supportImg: false
  },
  kwaipilot_7b: {
    maxContext: KWAIPILOT_32K_CONTEXT_MAX,
    maxResponse: 4096,
    supportImg: false
  },
  'claude-3-opus': {
    maxContext: 200 * 1000,
    maxResponse: 8192,
    supportImg: true
  }
};
