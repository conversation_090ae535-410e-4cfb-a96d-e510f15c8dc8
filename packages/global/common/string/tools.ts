import { FlowNodeOutputItemType } from '../../core/workflow/type/io';
import { WorkflowIOValueTypeEnum } from '../../core/workflow/constants';
import { RuntimeNodeItemType } from '../../core/workflow/runtime/type';
import crypto from 'crypto';
import { customAlphabet } from 'nanoid';

/* check string is a web link */
export function strIsLink(str?: string) {
  if (!str) return false;
  if (/^((http|https)?:\/\/|www\.|\/)[^\s/$.?#].[^\s]*$/i.test(str)) return true;
  return false;
}

/* hash string */
export const hashStr = (str: string) => {
  return crypto.createHash('sha256').update(str).digest('hex');
};

/* simple text, remove chinese space and extra \n */
export const simpleText = (text = '') => {
  text = text.trim();
  text = text.replace(/([\u4e00-\u9fa5])[\s&&[^\n]]+([\u4e00-\u9fa5])/g, '$1$2');
  text = text.replace(/\r\n|\r/g, '\n');
  text = text.replace(/\n{3,}/g, '\n\n');
  text = text.replace(/[\s&&[^\n]]{2,}/g, ' ');
  text = text.replace(/[\x00-\x08]/g, ' ');

  return text;
};

/* 
  replace {{variable}} to value
*/
export function replaceVariable(text: any, obj: Record<string, string | number>) {
  if (!(typeof text === 'string')) return text;

  for (const key in obj) {
    const val = obj[key];
    if (!['string', 'number'].includes(typeof val)) continue;

    text = text.replace(new RegExp(`{{(${key})}}`, 'g'), String(val));
  }
  return text || '';
}

function formatOutputValue(output: FlowNodeOutputItemType): string {
  if (!output.value) {
    return '';
  }

  if (
    output.valueType === WorkflowIOValueTypeEnum.number ||
    output.valueType === WorkflowIOValueTypeEnum.boolean ||
    output.valueType === WorkflowIOValueTypeEnum.string
  ) {
    return String(output.value);
  }

  try {
    return JSON.stringify(output.value);
  } catch (e: unknown) {
    return '';
  }
}

/**
 * 部分模版支持拼接一些节点的输出变量，因此需要将 output 也放到 variables 中
 */
export function replaceVariableAndOutput(
  text: any,
  obj: Record<string, string | number>,
  runtimeNodes: RuntimeNodeItemType[]
) {
  const variables = { ...obj };
  runtimeNodes.forEach((n) => {
    n.outputs.forEach((o) => {
      variables[`${n.nodeId}/${o.key}`] = formatOutputValue(o);
    });
  });

  return replaceVariable(text, variables);
}

/* replace sensitive text */
export const replaceSensitiveText = (text: string) => {
  // 1. http link
  text = text.replace(/(?<=https?:\/\/)[^\s]+/g, 'xxx');
  // 2. nx-xxx 全部替换成xxx
  text = text.replace(/ns-[\w-]+/g, 'xxx');

  return text;
};

export const getNanoid = (size = 12) => {
  return customAlphabet('abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890', size)();
};

export const replaceRegChars = (text: string) => text.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
