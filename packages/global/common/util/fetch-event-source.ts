import { fetchEventSource as fes } from '@fortaine/fetch-event-source';

type SSEArguments = Parameters<typeof fes>;

export function fetchEventSource(requestInfo: SSEArguments[0], sseInit: SSEArguments[1]) {
  const init = {
    ...sseInit,
    openWhenHidden: true,
    onerror: (e: any) => {
      sseInit.onerror?.(e);
      throw e;
    }
  };
  return fes(requestInfo, init);
}

export async function* readFromEventSource<T>(
  url: string,
  options: SSEArguments[1] & { parseData: (data: string, event: string) => T }
) {
  const messageQueue: T[] = [];
  const parseData = options.parseData;
  let resolveQueue: (() => void) | null = null;
  let rejectQueue: ((e: unknown) => void) | null = null;
  let isClosed = false;

  const resolveAndClearQueue = () => {
    if (resolveQueue) {
      resolveQueue();
      resolveQueue = null;
    }
  };

  fetchEventSource(url, {
    ...options,
    onmessage(e) {
      const d = parseData(e.data, e.event);
      messageQueue.push(d);
      resolveAndClearQueue();
    },
    onclose() {
      isClosed = true;
      resolveAndClearQueue();
    },
    onerror(e) {
      isClosed = true;
      if (rejectQueue) {
        rejectQueue(e);
        rejectQueue = null;
      }
    }
  });

  while (!isClosed || messageQueue.length > 0) {
    if (messageQueue.length > 0) {
      yield messageQueue.shift()!;
    } else {
      await new Promise<void>((resolve, reject) => {
        resolveQueue = resolve;
        rejectQueue = reject;
      });
    }
  }
}
