import { Ajv, JSONSchemaType } from 'ajv';
// import { logger } from './logger';

export * from 'ajv';

export const isValidJSONSchema = <T>(schema: string) => {
  try {
    const ajv = new Ajv();
    const obj = JSON.parse(schema);
    ajv.compile<T>(obj);
    return obj as JSONSchemaType<T>;
  } catch (e) {
    // FIXME: 兼容客户端和服务端场景
    // logger.error('Invalid JSON schema', e);
    return null;
  }
};

// 校验数据是否符合 Schema 定义
export const checkRequired = (schema: string, data: string) => {
  try {
    const ajv = new Ajv();
    const obj = JSON.parse(schema);
    const validate = ajv.compile(obj);
    const valid = validate(JSON.parse(data));
    if (!valid) {
      // 过滤出只与 required 相关的错误
      const requiredErrors = validate.errors?.filter((error) => error.keyword === 'required');
      if (requiredErrors?.length) {
        return false;
      }
    }
    return true;
  } catch (e) {
    // FIXME: 兼容客户端和服务端场景
    // logger.error('Invalid data by schema', e);
    return false;
  }
};
