import CacheableLookup from 'cacheable-lookup';
import https from 'node:https';
import http from 'node:http';

let cacheable: CacheableLookup | null = null;
export const installDNSCache = () => {
  if (cacheable) {
    return cacheable;
  }

  cacheable = new CacheableLookup({
    lookup: undefined,
    fallbackDuration: 0,
    maxTtl: 10
  });
  cacheable.install(http.globalAgent);
  cacheable.install(https.globalAgent);

  return cacheable;
};
